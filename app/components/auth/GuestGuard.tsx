import React, { useEffect } from "react";
import { router } from "expo-router";
import { useAuth } from "../../contexts/AuthContext";

interface GuestGuardProps {
  children: React.ReactNode;
  redirectTo?: string;
}

const GuestGuard: React.FC<GuestGuardProps> = ({
  children,
  redirectTo = "/",
}) => {
  const { isAuthenticated, isLoading } = useAuth();

  useEffect(() => {
    if (!isLoading && isAuthenticated) {
      router.replace(redirectTo);
    }
  }, [isAuthenticated, isLoading, redirectTo]);

  if (isLoading) {
    return null; // Let the main loading state handle this
  }

  if (isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  return <>{children}</>;
};

export default GuestGuard;
