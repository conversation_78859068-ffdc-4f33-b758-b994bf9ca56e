import { useTranslation } from "react-i18next";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import { SpindleConfig, useSpindleStore } from "@/store/useSpindleStore";
import { ScrollView, Text } from "react-native";
import { VStack } from "@/components/ui/vstack";
import { Button } from "@/components/ui/button";
import {
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  FormControlErrorIcon,
  Input,
  InputField,
} from "@/components/ui/form-control";
import { AlertTriangleIcon } from "lucide-react-native";

type FormValues = {
  s: string; // wifi ssid
  p: string; // wifi password
  name: string;
  tilt: string;
  interval: string;
};

const spindleSchema = z.object({
  s: z.string().min(1, { message: "WLAN Name ist erforderlich" }),
  p: z.string().min(1, { message: "WLAN Passwort ist erforderlich" }),
  name: z.string().min(1, { message: "Gerätename ist erforderlich" }),
  tilt: z.string().min(1, { message: "Neigungswinkel ist erforderlich" }),
  interval: z.string().min(1, { message: "Intervall ist erforderlich" }),
});

export default function SpindleScreen() {
  const { t } = useTranslation();

  const spindleConfig = useSpindleStore((state) => state.spindleConfig);

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: zodResolver(spindleSchema),
    defaultValues: spindleConfig ?? {
      s: "",
      p: "",
      name: "",
      tilt: "",
      interval: "",
    },
  });

  const onSubmit = (data: FormValues) => {
    useSpindleStore
      .getState()
      .mutateSpindleConfig(data as Partial<SpindleConfig>);
  };

  return (
    <ScrollView contentContainerStyle={{ flexGrow: 1, padding: 16 }}>
      <VStack space="md">
        {/* WiFi SSID */}
        <FormControl isInvalid={!!errors.s}>
          <FormControlLabel>
            <FormControlLabelText>{t("spindle.wifiSsid")}</FormControlLabelText>
          </FormControlLabel>
          {/* <Input className="my-1" size={props.size}>
            <InputField
              type="password"
              placeholder={t("spindle.wifiSsidPlaceholder")}
              //   value={inputValue}
              //   onChangeText={(text) => setInputValue(text)}
              onBlur={onBlur}
              onChangeText={onChange}
              value={value}
              autoCapitalize="none"
            />
          </Input> */}
          <Controller
            control={control}
            name="s"
            rules={{ required: t("spindle.wifiSsidRequired") as string }}
            render={({ field: { onChange, onBlur, value } }) => (
              <Input className="my-1" size={"md"}>
                <InputField
                  type="password"
                  placeholder={t("spindle.wifiSsidPlaceholder")}
                  //   value={inputValue}
                  //   onChangeText={(text) => setInputValue(text)}
                  onBlur={onBlur}
                  onChangeText={onChange}
                  value={value}
                  autoCapitalize="none"
                />
              </Input>
            )}
          />
          {errors.s && (
            <FormControlError>
              <FormControlErrorIcon as={AlertTriangleIcon} />
              <FormControlErrorText>{errors.s.message}</FormControlErrorText>
            </FormControlError>
          )}
        </FormControl>

        {/* WiFi Password */}
        <FormControl isInvalid={!!errors.p}>
          <FormControlLabel>{t("spindle.wifiPassword")}</FormControlLabel>
          <Controller
            control={control}
            name="p"
            rules={{ required: t("spindle.wifiPasswordRequired") as string }}
            render={({ field: { onChange, onBlur, value } }) => (
              <Input
                placeholder={t("spindle.wifiPasswordPlaceholder")}
                onBlur={onBlur}
                onChangeText={onChange}
                value={value}
                secureTextEntry
                autoCapitalize="none"
              />
            )}
          />
          {errors.p && (
            <FormControl.Error>
              <Text color="$red600">{errors.p.message}</Text>
            </FormControl.Error>
          )}
        </FormControl>

        {/* Device Name */}
        <FormControl isInvalid={!!errors.name}>
          <FormControlLabel>{t("spindle.deviceName")}</FormControlLabel>
          <Controller
            control={control}
            name="name"
            rules={{ required: t("spindle.deviceNameRequired") as string }}
            render={({ field: { onChange, onBlur, value } }) => (
              <Input
                placeholder={t("spindle.deviceNamePlaceholder")}
                onBlur={onBlur}
                onChangeText={onChange}
                value={value}
              />
            )}
          />
          {errors.name && (
            <FormControl.Error>
              <Text color="$red600">{errors.name.message}</Text>
            </FormControl.Error>
          )}
        </FormControl>

        {/* Tilt */}
        <FormControl isInvalid={!!errors.tilt}>
          <FormControlLabel>{t("spindle.tilt")}</FormControlLabel>
          <Controller
            control={control}
            name="tilt"
            rules={{ required: t("spindle.tiltRequired") as string }}
            render={({ field: { onChange, onBlur, value } }) => (
              <Input
                placeholder={t("spindle.tiltPlaceholder")}
                onBlur={onBlur}
                onChangeText={onChange}
                value={value}
                keyboardType="numeric"
              />
            )}
          />
          {errors.tilt && (
            <FormControl.Error>
              <Text color="$red600">{errors.tilt.message}</Text>
            </FormControl.Error>
          )}
        </FormControl>

        {/* Interval */}
        <FormControl isInvalid={!!errors.interval}>
          <FormControlLabel>{t("spindle.interval")}</FormControlLabel>
          <Controller
            control={control}
            name="interval"
            rules={{ required: t("spindle.intervalRequired") as string }}
            render={({ field: { onChange, onBlur, value } }) => (
              <Input
                placeholder={t("spindle.intervalPlaceholder")}
                onBlur={onBlur}
                onChangeText={onChange}
                value={value}
                keyboardType="numeric"
              />
            )}
          />
          {errors.interval && (
            <FormControl.Error>
              <Text color="$red600">{errors.interval.message}</Text>
            </FormControl.Error>
          )}
        </FormControl>

        <Button onPress={handleSubmit(onSubmit)}>
          <Text>{t("spindle.save")}</Text>
        </Button>
      </VStack>
    </ScrollView>
  );
}
