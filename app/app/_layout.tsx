// import FontAwesome from "@expo/vector-icons/FontAwesome";
import "@/global.css";
import { GluestackUIProvider } from "@/components/ui/gluestack-ui-provider";
// import { useFonts } from "expo-font";
import { Stack } from "expo-router";
// import * as SplashScreen from "expo-splash-screen";
// import { useEffect } from "react";
import { QueryClientProvider } from "@tanstack/react-query";
// import "react-native-reanimated";
import "@/helpers/i18n";

import queryClient from "@/helpers/reactQuery";
import useAuthStore from "@/store/useAuthStore";
import { useTranslation } from "react-i18next";
import { WidgetProvider } from "@/providers/WidgetProvider";
// import Ionicons from "@expo/vector-icons/Ionicons";
import { SatelliteDish } from "lucide-react-native";

//

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from "expo-router";

// export const unstable_settings = {
//   // Ensure that reloading on `/modal` keeps a back button present.
//   initialRouteName: "(tabs)",
// };

// Prevent the splash screen from auto-hiding before asset loading is complete.
// SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  // const [loaded, error] = useFonts({
  //   SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  //   ...FontAwesome.font,
  // });

  // Expo Router uses Error Boundaries to catch errors in the navigation tree.
  // useEffect(() => {
  //   if (error) throw error;
  // }, [error]);

  // useEffect(() => {
  //   if (loaded) {
  //     SplashScreen.hideAsync();
  //   }
  // }, [loaded]);

  // if (!loaded) {
  //   return null;
  // }

  return (
    <QueryClientProvider client={queryClient}>
      <GluestackUIProvider mode="light">
        <WidgetProvider>
          <RootLayoutNav />
        </WidgetProvider>
      </GluestackUIProvider>
    </QueryClientProvider>
  );
}

function RootLayoutNav() {
  const { t } = useTranslation();

  const isAuthenticated = useAuthStore((state) => state.isAuthenticated);

  return (
    <Stack>
      <Stack.Protected guard={!isAuthenticated}>
        <Stack.Screen
          name="auth/index"
          options={{ headerShown: false, title: t("auth.authentication") }}
        />
      </Stack.Protected>
      <Stack.Protected guard={isAuthenticated}>
        <Stack.Screen
          name="index"
          options={{
            headerShown: true,
            title: t("app.home"),
            headerRight: () => <SatelliteDish size={24} color="black" />,
          }}
        />
        <Stack.Screen
          name="fermentation/index"
          options={{ headerShown: true, title: t("fermentation.title") }}
        />
        <Stack.Screen
          name="fermentation/device/[deviceId]"
          options={{
            headerShown: false,
            title: t("fermentation.deviceDetail"),
          }}
        />
        <Stack.Screen
          name="device/index"
          options={{
            headerShown: true,
            title: t("device.management"),
          }}
        />
        <Stack.Screen
          name="spindle/index"
          options={{
            headerShown: true,
            title: t("device.management"),
          }}
        />
      </Stack.Protected>
      <Stack.Screen name="+not-found" />
    </Stack>
  );
}
