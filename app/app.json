{"expo": {"name": "<PERSON><PERSON><PERSON><PERSON>", "slug": "g<PERSON><PERSON><PERSON>", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "g<PERSON><PERSON><PERSON>", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.gaerschrank.app", "entitlements": {"com.apple.security.application-groups": ["group.gaerschrank.widget"]}, "infoPlist": {"NSLocalNetworkUsageDescription": "The app requires access to the local network so it can discover iSpindels and BrewTools to WiFi."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_CONNECT", "android.permission.BIND_APPWIDGET", "android.permission.ACCESS_FINE_LOCATION"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-localization", "expo-secure-store", "expo-background-task", "react-native-wifi-reborn"], "experiments": {"typedRoutes": true}}}