{"name": "app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "DARK_MODE=media expo start --android", "ios": "DARK_MODE=media expo start --ios", "web": "DARK_MODE=media expo start --web", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/html-elements": "^0.12.5", "@expo/vector-icons": "^14.1.0", "@gluestack-ui/alert": "^0.1.16", "@gluestack-ui/button": "^1.0.14", "@gluestack-ui/icon": "^0.1.27", "@gluestack-ui/input": "^0.1.38", "@gluestack-ui/modal": "^0.1.41", "@gluestack-ui/nativewind-utils": "^1.0.28", "@gluestack-ui/overlay": "^0.1.22", "@gluestack-ui/toast": "^1.0.9", "@hookform/resolvers": "^5.2.1", "@legendapp/motion": "^2.4.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.85.5", "axios": "^1.11.0", "babel-plugin-module-resolver": "^5.0.2", "date-fns": "^4.1.0", "domutils": "^3.2.2", "expo": "~53.0.22", "expo-background-task": "~0.2.8", "expo-crypto": "~14.1.5", "expo-font": "~13.3.2", "expo-linking": "~7.1.7", "expo-localization": "~16.1.6", "expo-notifications": "~0.31.4", "expo-router": "~5.1.5", "expo-secure-store": "~14.2.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.11", "expo-task-manager": "~13.1.6", "expo-web-browser": "~14.2.0", "htmlparser2": "^10.0.0", "i18next": "^25.4.2", "lucide-react-native": "^0.542.0", "nativewind": "^4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.62.0", "react-i18next": "^15.7.2", "react-native": "0.79.6", "react-native-css-interop": "^0.1.18", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "react-native-wifi-reborn": "^4.13.6", "react-native-worklets": "^0.4.1", "tailwindcss": "^3.4.17", "zod": "^4.1.3", "zustand": "^5.0.8"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "jest": "^29.2.1", "jest-expo": "~53.0.10", "jscodeshift": "0.15.2", "prettier-plugin-tailwindcss": "^0.5.11", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true}