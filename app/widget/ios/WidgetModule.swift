import Foundation
import WidgetKit

@objc(WidgetModule)
class WidgetModule: NSObject {
  
  @objc
  static func requiresMainQueueSetup() -> <PERSON><PERSON> {
    return false
  }
  
  @objc
  func updateWidgetData(_ data: NSDictionary, resolver: @escaping RCTPromiseResolveBlock, rejecter: @escaping RCTPromiseRejectBlock) {
    
    guard let userDefaults = UserDefaults(suiteName: "group.gaerschrank.widget") else {
      rejecter("ERROR", "Failed to access UserDefaults with app group", nil)
      return
    }
    
    do {
      // Convert NSDictionary to WidgetData and then to JSON
      let widgetData = WidgetData(
        fermentationName: data["fermentationName"] as? String ?? "Unknown",
        currentTemperature: data["currentTemperature"] as? Double,
        targetTemperature: data["targetTemperature"] as? Double,
        gravity: data["gravity"] as? Double,
        lastUpdate: data["lastUpdate"] as? String ?? "",
        status: data["status"] as? String ?? "offline",
        deviceId: data["deviceId"] as? Int
      )
      
      let jsonData = try JSONEncoder().encode(widgetData)
      userDefaults.set(jsonData, forKey: "widget_fermentation_data")
      userDefaults.synchronize()
      
      // Reload all widgets
      WidgetCenter.shared.reloadAllTimelines()
      
      resolver(nil)
    } catch {
      rejecter("ENCODING_ERROR", "Failed to encode widget data", error)
    }
  }
  
  @objc
  func refreshWidget(_ resolver: @escaping RCTPromiseResolveBlock, rejecter: @escaping RCTPromiseRejectBlock) {
    WidgetCenter.shared.reloadAllTimelines()
    resolver(nil)
  }
  
  @objc
  func isWidgetSupported(_ resolver: @escaping RCTPromiseResolveBlock, rejecter: @escaping RCTPromiseRejectBlock) {
    resolver(true)
  }
}

struct WidgetData: Codable {
  let fermentationName: String
  let currentTemperature: Double?
  let targetTemperature: Double?
  let gravity: Double?
  let lastUpdate: String
  let status: String
  let deviceId: Int?
}
