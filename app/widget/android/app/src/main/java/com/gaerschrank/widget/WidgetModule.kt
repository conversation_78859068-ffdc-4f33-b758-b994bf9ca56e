package com.gaerschrank.widget

import com.facebook.react.bridge.*
import com.facebook.react.module.annotations.ReactModule

@ReactModule(name = "WidgetModule")
class WidgetModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {

    override fun getName(): String {
        return "WidgetModule"
    }

    @ReactMethod
    fun updateWidgetData(data: ReadableMap, promise: Promise) {
        try {
            val widgetData = WidgetData(
                fermentationName = data.getString("fermentationName") ?: "Unknown",
                currentTemperature = if (data.hasKey("currentTemperature") && !data.isNull("currentTemperature")) 
                    data.getDouble("currentTemperature") else null,
                targetTemperature = if (data.hasKey("targetTemperature") && !data.isNull("targetTemperature")) 
                    data.getDouble("targetTemperature") else null,
                gravity = if (data.hasKey("gravity") && !data.isNull("gravity")) 
                    data.getDouble("gravity") else null,
                lastUpdate = data.getString("lastUpdate") ?: "",
                status = data.getString("status") ?: "offline",
                deviceId = if (data.hasKey("deviceId") && !data.isNull("deviceId")) 
                    data.getInt("deviceId") else null
            )

            FermentationWidgetProvider.updateWidgetData(reactApplicationContext, widgetData)
            promise.resolve(null)
        } catch (e: Exception) {
            promise.reject("UPDATE_ERROR", "Failed to update widget data", e)
        }
    }

    @ReactMethod
    fun refreshWidget(promise: Promise) {
        try {
            // Trigger widget update by sending broadcast
            val context = reactApplicationContext
            val appWidgetManager = android.appwidget.AppWidgetManager.getInstance(context)
            val widgetIds = appWidgetManager.getAppWidgetIds(
                android.content.ComponentName(context, FermentationWidgetProvider::class.java)
            )
            
            for (widgetId in widgetIds) {
                FermentationWidgetProvider.updateAppWidget(context, appWidgetManager, widgetId)
            }
            
            promise.resolve(null)
        } catch (e: Exception) {
            promise.reject("REFRESH_ERROR", "Failed to refresh widget", e)
        }
    }

    @ReactMethod
    fun isWidgetSupported(promise: Promise) {
        promise.resolve(true)
    }
}
