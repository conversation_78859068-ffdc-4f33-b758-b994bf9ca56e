<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/widget_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@drawable/widget_background">

    <!-- Header with fermentation name and status -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <TextView
            android:id="@+id/fermentation_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Gärung"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_primary"
            android:maxLines="1"
            android:ellipsize="end" />

        <TextView
            android:id="@+id/status"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Aktiv"
            android:textSize="12sp"
            android:textStyle="bold"
            android:textColor="@color/widget_status_active"
            android:background="@drawable/status_background"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp" />

    </LinearLayout>

    <!-- Temperature section -->
    <LinearLayout
        android:id="@+id/temperature_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="4dp">

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_thermometer"
            android:tint="@color/widget_icon_temperature"
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/current_temperature"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="19.2°C"
            android:textSize="18sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_primary" />

        <TextView
            android:id="@+id/target_temperature"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Ziel: 19.0°C"
            android:textSize="12sp"
            android:textColor="@color/widget_text_secondary"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Gravity section -->
    <LinearLayout
        android:id="@+id/gravity_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="8dp">

        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@drawable/ic_drop"
            android:tint="@color/widget_icon_gravity"
            android:layout_marginEnd="8dp" />

        <TextView
            android:id="@+id/gravity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="12.5°P"
            android:textSize="16sp"
            android:textStyle="bold"
            android:textColor="@color/widget_text_primary" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Stammwürze"
            android:textSize="12sp"
            android:textColor="@color/widget_text_secondary"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <!-- Spacer -->
    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1" />

    <!-- Last update -->
    <TextView
        android:id="@+id/last_update"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Aktualisiert vor 2 Min"
        android:textSize="10sp"
        android:textColor="@color/widget_text_secondary"
        android:gravity="end" />

</LinearLayout>
