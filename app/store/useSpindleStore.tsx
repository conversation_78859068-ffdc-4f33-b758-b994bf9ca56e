import axios from "axios";
import WifiManager from "react-native-wifi-reborn";
import * as htmlparser2 from "htmlparser2";
import { html } from "@/services/DevService";
import { getAttributeValue, getElementById } from "domutils";
import { create } from "zustand";
import { useEffect } from "react";
import { API_URL } from "./useAuthStore";

export interface SpindleConfig {
  s: string; // wifi ssid
  p: string; // wifi password
  name: string;
  sleep: number;
  vfact: number;
  tempscale: number;
  selAPI: number;
  warning1: string;
  token: string;
  server: string;
  port: number;
  channel: number;
  uri: string;
  username: string;
  password: string;
  job: string;
  instance: string;
  POLYN: string;
}

const FormKeys = [
  "s",
  "p",
  "name",
  "sleep",
  "vfact",
  "tempscale",
  "selAPI",
  "warning1",
  "token",
  "server",
  "port",
  "channel",
  "uri",
  "username",
  "password",
  "job",
  "instance",
  "POLYN",
] as (keyof SpindleConfig)[];

const getSpindleWifiConfig = (html: string) => {
  const dom = htmlparser2.parseDocument(html);

  const result = {} as SpindleConfig;
  for (const key of FormKeys) {
    const input = getElementById(key, dom);

    if (!input) {
      result[key] = "";
      continue;
    }

    result[key] = getAttributeValue(input, "value") ?? "";
  }

  return result;
};

type SpindleState = {
  spindleConfig: SpindleConfig | null;
  isRegistered: boolean;
  isConnected: boolean;
  setSpindleConfig: (config: SpindleConfig) => void;
  mutateSpindleConfig: (data: unknown) => void;
};

export const useSpindleStore = create<SpindleState>()((set, get) => {
  const saveSpindleWifi = async (data: SpindleConfig) => {
    try {
      const searchParams = new URLSearchParams();
      for (const key of FormKeys) {
        searchParams.append(key, String(data[key]));
      }

      const response = await axios.get(
        "http://192.168.4.1/wifisave?" + searchParams.toString()
      );
      console.log(response.data);

      set({ spindleConfig: null, isConnected: false, isRegistered: false });
    } catch (error) {
      console.error("Error fetching WiFi config:", error);
    }
  };

  const mutateSpindleConfig = (data: Partial<SpindleConfig>) => {
    const actualConfig = get().spindleConfig;

    const request = { ...actualConfig, ...data } as SpindleConfig;

    saveSpindleWifi(request);
  };

  return {
    spindleConfig: null,
    isRegistered: false,
    isConnected: false,
    mutateSpindleConfig,
    setSpindleConfig: (config) => {
      const url = new URL(API_URL);
      const isRegistered = config.server.startsWith(url.origin);
      set({ spindleConfig: config, isRegistered, isConnected: true });
    },
  };
});

export const useRecognizeSpindle = () => {
  useEffect(() => {
    (async () => {
      try {
        const currentSSID = await WifiManager.getCurrentWifiSSID();

        if (currentSSID.toLowerCase().includes("ispindle")) {
          axios.get("http://192.168.4.1/wifi").then((res) => {
            console.log(res.data);
            const result = getSpindleWifiConfig(res.data);
            useSpindleStore.getState().setSpindleConfig(result);
          });
        }

        console.log(`Your current Wi-Fi SSID is ${currentSSID}`);
      } catch (error) {
        console.log("Cannot get current SSID!");

        const result = getSpindleWifiConfig(html);
        console.log(result);
        useSpindleStore.getState().setSpindleConfig(result);
      }
    })();
  }, []);
};
