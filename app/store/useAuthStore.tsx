import { create } from "zustand";
import { persist, createJSONStorage, StateStorage } from "zustand/middleware";
import axios, { AxiosInstance } from "axios";
import { deleteItemAsync, getItemAsync, setItemAsync } from "expo-secure-store";

export const API_URL = "https://gaerschrank.philun.de/api";

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  token: string | null;
  refreshToken?: string;
  expiresAt?: number;
  api: AxiosInstance | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  register: (credentials: RegisterCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refresh: () => Promise<void>;
}

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  name: string;
}

class SecureStoreStorage implements StateStorage {
  getItem(name: string) {
    return getItemAsync(name);
  }
  setItem(name: string, value: string) {
    return setItemAsync(name, value);
  }
  removeItem(name: string) {
    return deleteItemAsync(name);
  }
}

const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => {
      return {
        user: null,
        isLoading: false,
        isAuthenticated: false,
        api: null,
        refresh: async () => {
          const refreshToken = get().refreshToken;
          if (!refreshToken) throw new Error("No refresh token available");
          try {
            const response = await axios.post(API_URL + "/auth/refresh", {
              refreshToken,
            });
            const data = response.data;
            const api = createApiInstance(data.token, set, get);
            set({
              token: data.token,
              refreshToken: data.refreshToken,
              expiresAt: data.expiresAt,
              isAuthenticated: true,
              api,
            });
          } catch (error) {
            // If refresh fails, log out
            await get().logout();
            throw error;
          }
        },
        login: async (credentials: LoginCredentials) => {
          try {
            const response = await axios.post(API_URL + "/login", credentials);
            const data = response.data;

            const api = createApiInstance(data.token, set, get);

            set({
              user: data.user,
              token: data.token,
              refreshToken: data.refreshToken,
              expiresAt: data.expiresAt,
              isAuthenticated: true,
              api,
            });
          } catch (error) {
            console.error("Login error:", error);
            throw error;
          }
        },

        register: async (credentials: RegisterCredentials) => {},

        logout: async () => {
          set({
            user: null,
            token: null,
            refreshToken: null,
            expiresAt: null,
            isAuthenticated: false,
            api: null,
          });
        },
      };
    },
    {
      name: "auth-storage",
      storage: createJSONStorage(() => new SecureStoreStorage()),
      partialize: (state) => {
        const { api, ...persistableState } = state;
        return persistableState;
      },
    }
  )
);

// Helper to create axios instance with interceptor
function createApiInstance(token: string, set: any, get: any) {
  const api = axios.create({
    baseURL: API_URL,
    headers: {
      Authentication: `Bearer ${token}`,
    },
  });

  // Add interceptor for 401 to refresh token
  api.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;
      if (
        error.response &&
        error.response.status === 401 &&
        !originalRequest._retry
      ) {
        originalRequest._retry = true;
        try {
          await get().refresh();
          const newToken = get().token;
          originalRequest.headers["Authentication"] = `Bearer ${newToken}`;
          return api(originalRequest);
        } catch (refreshError) {
          await get().logout();
          return Promise.reject(refreshError);
        }
      }
      return Promise.reject(error);
    }
  );
  return api;
}

// On hydration, check token expiration and refresh if needed
useAuthStore.persist.onFinishHydration(async () => {
  const state = useAuthStore.getState();
  const now = Date.now() / 1000;
  let token = state.token;
  let expiresAt = state.expiresAt;
  let api = null;

  if (token && expiresAt && expiresAt < now) {
    // Token expired, try to refresh
    try {
      await state.refresh();
      token = useAuthStore.getState().token;
      expiresAt = useAuthStore.getState().expiresAt;
    } catch {
      // Refresh failed, user is logged out
      return;
    }
  }

  if (token) {
    api = createApiInstance(
      token,
      useAuthStore.setState,
      useAuthStore.getState
    );
    useAuthStore.setState({ api });
  }
});

export default useAuthStore;
