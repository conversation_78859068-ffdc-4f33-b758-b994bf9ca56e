{"translation": {"appname": "<PERSON><PERSON><PERSON><PERSON>", "common": {"date": "Datum", "cancel": "Abbrechen", "close": "Schließen", "deactivate": "Deaktivieren", "delete": "Löschen", "discard": "Verwerfen", "discardChanges": "<PERSON>s gibt nicht gespeicherte Änderungen", "discardChangesDescription": "Sind <PERSON> sicher, dass Sie Ihre Änderungen verwerfen und diese Seite verlassen möchten?", "discardDeleteDescription": "Diese Aktion kann nicht rückgängig gemacht werden.", "edit": "<PERSON><PERSON><PERSON>", "errorOccured": "Es ist ein unerwarteter Fehler aufgetreten.", "loading": "Laden...", "menu": "<PERSON><PERSON>", "no": "<PERSON><PERSON>", "requiredField": "{{label}} (<PERSON><PERSON><PERSON><PERSON>)", "save": "Speichern", "yes": "<PERSON>a", "continue": "<PERSON><PERSON>", "activate": "Aktivieren", "dismiss": "Schließen", "on": "Einschahlten", "off": "Ausschalten", "upload": "Hochladen", "newEntry": "<PERSON><PERSON><PERSON> Eintrag", "noItems": "<PERSON><PERSON> Ein<PERSON>ä<PERSON> vorhanden", "createNewText": "Füge einen neuen Eintrag hinzu", "openContextMenu": "Ö<PERSON>ne <PERSON>", "deleteDescription": "Möchten Sie diesen Eintrag wirklich löschen?", "amount": "<PERSON><PERSON>", "name": "Name", "temperature": "Temperatur", "open": "<PERSON><PERSON><PERSON>", "password": "Passwort", "username": "<PERSON><PERSON><PERSON><PERSON>", "login": "Anmelden", "register": "Registrieren", "forgotPassword": "Passwort vergessen?", "email": "E-Mail", "heating": "<PERSON><PERSON><PERSON><PERSON>", "cooling": "Kühlung", "createdAt": "Erstellt am {{date}}", "recreate": "<PERSON><PERSON><PERSON><PERSON>", "standBy": "Standby", "start": "Starten", "table": "<PERSON><PERSON><PERSON>", "graph": "<PERSON><PERSON>", "connect": "Verbinden", "fieldRequired": "<PERSON><PERSON> ist erforderlich.", "saving": "Speichern...", "updating": "Aktualisieren...", "adding": "Hinzufügen...", "never": "<PERSON><PERSON>", "justNow": "Gerade eben", "minutesAgo_one": "vor {{count}} Minute", "minutesAgo_other": "vor {{count}} Minuten", "hoursAgo_one": "vor {{count}} Stunde", "hoursAgo_other": "vor {{count}} <PERSON><PERSON><PERSON>", "daysAgo_one": "vor {{count}} Tag", "daysAgo_other": "vor {{count}} <PERSON>en", "online": "Online", "offline": "Offline", "connected": "Verbunden", "disconnected": "Getrennt", "search": "<PERSON><PERSON>", "back": "Zurück", "unknown": "Unbekannt", "enabled": "Aktiviert", "noNotifications": "<PERSON><PERSON>", "add": "Hinzufügen", "moreActions": "Ö<PERSON>ne <PERSON>"}, "spindle": {"basicConfig": "Grundkonfiguration", "spindleName": "Spindel Name", "wifiSSID": "WLAN-Name (SSID)", "wifiPassword": "WLAN-Passwort", "registerWithSite": "<PERSON><PERSON> dieser Seite registrieren und verwenden", "registerInfo": "Dies konfiguriert deine iSpindle automatisch, um Daten an diese Seite zu senden und generiert einen API-Schlüssel.", "advancedConfig": "Erweiterte Konfiguration", "serverAddress": "Server-<PERSON><PERSON><PERSON>", "serverPort": "Server-Port", "serverPath": "Server-Pfad", "apiToken": "API-Token", "sleepInterval": "Schlafintervall (Sekunden)", "temperatureScale": "Temperaturskala (0=Celsius, 1=Fahrenheit)", "removeWifiTitle": "WLAN-Verbindung entfernen", "removeWifiDescription": "Bitte trenne die Verbindung zum iSpindle-WLAN und klicke auf Wei<PERSON>, um die Registrierung abzuschließen.", "registrationSuccessTitle": "Registrierung erfolgreich", "registrationSuccessDescription": "Deine iSpindle wurde erfolgreich registriert und mit dieser Seite konfiguriert.", "finish": "Fertigstellen", "connectedToSpindle": "Mit iSpindle verbunden"}, "navigation": {"dashboard": "Dashboard", "brewing": "<PERSON><PERSON><PERSON>", "fermentation": "Gärung", "inventory": "Inventar", "setting": "Einstellungen", "recipe": "Rezepte", "notifications": "Benachrichtigungen", "device": "<PERSON><PERSON><PERSON><PERSON>", "userSettings": "Einstellungen", "logout": "Abmelden", "brewlog": "<PERSON><PERSON><PERSON><PERSON>", "integrations": "Integrationen"}, "fermentation": {"temperature": "Temperatur", "hasPushOnReached": "Push Meldung bei Erreichen der Temperatur", "name": "<PERSON><PERSON><PERSON><PERSON>", "formDescription": "Setzte die gewünschte Temperatur für den Gärschrank", "coldCrush": "Cold Crush", "shouldTemperature": "<PERSON><PERSON><PERSON>", "isTemperature": "Ist", "workingUnlimited": "l<PERSON><PERSON>t unbefristet", "noActiveFermentation": "Keine aktive Gärung", "noActiveFermentationHelp": "Starte eine Gärung vom Rezept aus", "startFermentation": "Gärung starten", "activeFermentation": "Gärung läuft", "selectDevices": "Gerät fürs Gären", "monitor": "Gärungsmonitor", "trackAndControl": "Überwache und kontrolliere deine Fermentationsgeräte", "devices": "<PERSON><PERSON><PERSON><PERSON>", "noDevicesFoundDescription": "Verbinde dein erstes Brewtool-Gerät über Bluetooth", "currentTemperature": "Aktuelle Temperatur", "targetTemperature": "Zieltemperatur", "spindleInformation": "Spindel Information", "plato": "<PERSON>", "spindleName": "Spindel Name", "notAvailable": "Nicht verfügbar", "coldCrash": "Cold Crash", "turnOff": "Ausschalten", "setPlatoAlert": "Plato-Al<PERSON> e<PERSON>tellen", "statistics": "Statistiken", "time": "Zeit", "simplifiedStats": "Dies ist eine vereinfachte Statistikansicht. In einer echten Anwendung würde hier ein richtiges Diagramm angezeigt werden.", "unnamedDevice": "Unbenanntes Gerät", "deviceNotFound": "Gerät nicht gefunden", "backToDashboard": "Zurück zum Dashboard", "back": "Zurück", "title": "Titel", "platoThreshold": "<PERSON> (°P)", "currentPlato": "Aktueller Plato", "setPlatoAlertTitle": "Plato-Al<PERSON> e<PERSON>tellen", "platoAlertDescription": "Sie erhalten eine Benachrichtigung, wenn der Plato-<PERSON>rt den von Ihnen festgelegten Schwellenwert erreicht.", "noDevicesFound": "<PERSON><PERSON> G<PERSON> gefunden", "recipeName": "Rezeptname", "noSpindleConnected": "<PERSON>ine <PERSON>del verbunden. Verbinde eine Spindel, um den Plato-Wert zu überwachen.", "connectSpindle": "Spindel verbinden", "temperatureChangeLastHour": "Temperaturänderung in der letzten Stunde: {{value}} °C", "notificationPowerOff": "Gärung ausgeschaltet", "notificationTemperatureSet": "Gärung auf {{temperature}} °C eingestellt", "selectSpindle": "Spindel au<PERSON>wählen", "selectableSpindle": "<PERSON><PERSON>", "spindelAlreadyConnected": "Spindel ist bereits mit {{deviceName}} verbunden", "spindleConnected": "Spindel verbunden", "noSpindleData": "<PERSON><PERSON> verfü<PERSON>", "gravityChangeLastHour": "Gravitätsänderung in der letzten Stunde: {{value}} °P", "editBrewtool": "{{name}} bearbeiten", "deviceUpdated": "Gerät erfolgreich aktualisiert", "deviceName": "G<PERSON><PERSON><PERSON><PERSON>", "deviceNamePlaceholder": "<PERSON><PERSON> einen Namen für das Gerät ein", "addManualGravity": "Gravität hinzufügen", "gravityValue": "Gravitätswert", "gravityAdded": "Gravität erfolgreich hinzugefügt", "gravityAddError": "Fehler beim Hinzufügen der Gravität", "editTemperature": "Temperatur bearbeiten", "temperatureUpdated": "Temperatur aktualisiert", "temperatureUpdateError": "Fehler beim Aktualisieren der Temperatur", "newTargetTemperature": "Neue Zieltemperatur", "updateTemperature": "Temperatur aktualisieren", "brewLog": "Gärprotokoll", "completedFermentations": "Abgeschlossene Gärungen", "searchFermentations": "Gärungen suchen...", "fermentationsFound": "Gärungen gefunden", "noCompletedFermentations": "<PERSON><PERSON> abgeschlossenen Gärungen", "noCompletedFermentationsDescription": "Hier werden deine abgeschlossenen Gärprozesse angezeigt", "unnamedFermentation": "Unbenannte Gärung", "unknownDevice": "Unbekanntes Gerät", "completed": "Abgeschlossen", "averageTemperature": "Durchschnittstemperatur", "temperatureRange": "Temperaturbereich", "duration": "<PERSON><PERSON>", "fermentationPeriod": "Gärungszeitraum", "startDate": "Startdatum", "endDate": "Enddatum", "ongoing": "<PERSON><PERSON><PERSON>", "temperatureHistory": "Temperaturver<PERSON>f", "settings": "Einstellungen", "gravityAlert": "Stammwürze-Alarm", "pushNotifications": "Push-Benachrichtigungen", "loadingStatistics": "Lade Messdaten..."}, "date": {"days_one": "{{count}} Tag", "days_other": "{{count}} <PERSON>e", "months_one": "{{count}} <PERSON>t", "months_other": "{{count}} <PERSON><PERSON>", "years_one": "{{count}} Jahr", "years_other": "{{count}} J<PERSON>re"}, "time": {"hours_one": "{{count}} St<PERSON><PERSON>", "hours_other": "{{count}} <PERSON><PERSON><PERSON>", "seconds_one": "{{count}} Se<PERSON><PERSON>", "seconds_other": "{{count}} <PERSON><PERSON><PERSON>", "minutes_one": "{{count}} Minute", "minutes_other": "{{count}} Minuten"}, "hop": {"title": "Hopfen", "defaultUnit": "g", "alpha": "Alpha (in %)"}, "yeast": {"title": "<PERSON><PERSON>", "defaultUnit": "Packung"}, "malt": {"title": "Malz", "defaultUnit": "kg"}, "inventory": {"name": "Inventar", "everything": "Alles", "newEntry": "<PERSON><PERSON><PERSON> Eintrag", "noItems": "<PERSON><PERSON>", "createNewText": "Füge neue Bestände hinzu, um deinen Vorrat zu verwalten", "manageStock": "<PERSON><PERSON> ver<PERSON>ten", "type": "<PERSON><PERSON>", "nameLabel": "Name", "amount": "<PERSON><PERSON>", "unit": "Einheit", "malt": {"title": "Malz", "defaultUnit": "kg"}, "other": {"title": "Sonstiges", "defaultUnit": "Stück"}}, "brew": {"name": "<PERSON><PERSON><PERSON>", "start": "Sud kochen", "boilWort": "<PERSON><PERSON><PERSON><PERSON> kochen", "boilMash": "Maischeplan", "boilHop": "Hopfengaben", "fermentation": "Gärführung", "ingredients": "<PERSON><PERSON><PERSON><PERSON>", "boil": "<PERSON><PERSON>", "single": "Sud", "timer_zero": "{{m}}:{{s}}", "timer_other": "{{m}}:{{s}} von {{count}} min", "timerTitle": "{{name}} bei {{temperature}} °C ({{type}})", "mashboil": "Maischekochen", "mashing": "Maischen", "actualProcess": "Laufende Prozesse", "noProcessAtTheMoment": "<PERSON><PERSON> laufenden Prozesse im Moment", "selectDevices": "Gerät fürs Brauen", "createdByRecipe": "Erstellt aus Rezept: ", "heating": "<PERSON><PERSON><PERSON><PERSON> auf {{temperature}} °C", "startMashing": "Maischevorgang starten", "fermentables": "<PERSON><PERSON><PERSON>", "notifications": {"hopsTitle": "Hopfen zum Kochen hinzufügen", "hopsText": "<PERSON>üge den Hopfen {{hop}} zum Kochen hinzu"}, "startBoil": "Kochvorgang starten", "boilTimer": "Kochvorgang l<PERSON>"}, "recipe": {"ingredients": "<PERSON><PERSON><PERSON><PERSON>", "name": "Rezepte", "single": "Rezept", "uploadRecipe": "<PERSON><PERSON><PERSON> ho<PERSON>laden", "uploadDescription": "Lade ein BeerXML, PDF oder Bild hoch, um ein Rezept zu erstellen", "file": "<PERSON><PERSON>", "createdAt": "Erstellt am {{date}}", "cooked": "Gekocht", "inProgress": "In Arbeit", "notCooked": "<PERSON><PERSON> gek<PERSON>t", "createdByBeerXml": "Erstellt aus BeerXML", "viewRecipe": "<PERSON><PERSON><PERSON>", "cook": "Sud kochen", "fermentate": "G<PERSON><PERSON>rank starten", "inInventory": "<PERSON><PERSON>", "optimalTemperature": "Optimale Temperatur", "downloadOrPrint": "Rezept download oder drucken", "messageFermentationStarted": "Gärschrank mit der Temperatur {{temperature}} °C gestartet", "dryHop": "Hopfenstopfen", "firstWort": "Vorderwürzhopfen", "hopOnBoil": "{{time}} <PERSON><PERSON><PERSON> vor <PERSON>", "hopOnWhirlpool": "Whirlpool", "showAmountHops": "Zeige Gesamtmenge an Hopfen", "showHopsRecipe": "Zeige Hopfen nach Rezept", "time": "<PERSON><PERSON><PERSON>", "ibu": "IBU", "alcohol": "Alkohol", "yeastNotFoundSelectAnother": "<PERSON><PERSON> nicht gefunden, bitte wähle die Hefe aus der Liste aus", "startBrew": "Sud starten", "start": "Starten", "allTimeForToday": "Gesamtzeit: {{time}} min", "steps": "<PERSON><PERSON><PERSON>", "boil": "Maischeplan", "hops": "Hopfengaben", "dryHopping": "Hopfenstopfen", "fermentation": "Gärführung", "importedFromBraureka": "Importiert aus Braureka", "openBraureka": "Öffne Rezept in Braureka", "mashStep": " bei {{temp}} °C für {{time}} min ({{type}})", "openRecipe": "<PERSON><PERSON><PERSON>", "recipeCreated": "Rezept erfolgreich aus dem Upload erstellt", "noFermentationTemperatureExist": "<PERSON>ine Gärtemperatur gefunden", "manageAndView": "Verwalte und betrachte deine Rezepte", "noRecipes": "<PERSON><PERSON>", "uploadToStart": "Lade ein Rezept als BeerXML, PDF oder Bild hoch, um zu starten", "uploadAiDescription": "Mit Hilfe künstlicher Inteligenz wird das Rezept aus der Datei importiert, aber auch eine KI kann Fehler machen!", "deleteDescription": "Möchten Sie diesen Eintrag wirklich löschen?", "notFoundTitle": "Rezept nicht gefunden", "notFoundDescription": "Bitte überprüfe die URL oder wähle ein anderes Rezept.", "errorLoading": "<PERSON>im <PERSON>den des Rezepts ist ein Fehler aufgetreten.", "editRecipe": "<PERSON><PERSON><PERSON> bearbeiten", "generateLogo": "<PERSON><PERSON> gene<PERSON>", "generateBottleLabel": "Flaschenetikett generieren", "brewer": "<PERSON><PERSON><PERSON>", "overview": "Übersicht", "uploadLogo": "Logo hochladen", "style": "Stil", "noStyle": "<PERSON><PERSON>", "logoPromptLabel": "Was soll auf dem Logo zu sehen sein?", "logoPromptPlaceholder": "<PERSON><PERSON><PERSON><PERSON>, was auf dem Logo zu sehen sein soll...", "includeInformation": "<PERSON><PERSON> e<PERSON>nden", "includeAlcohol": "Alkoholgehalt", "includeBitterness": "Bitterkeit (IBU)", "includePlato": "<PERSON>", "includeHops": "Hopfen", "generateWithAI": "Mit KI generieren", "generating": "Generiere Bild...", "deleteImage": "Bild löschen", "logoGeneratedSuccess": "Logo erfolgreich generiert!", "bottleLabelGeneratedSuccess": "Flaschenetikett erfolgreich generiert!", "failedToGenerateImage": "Bild konnte nicht generiert werden", "failedToDeleteImage": "Bild konnte nicht gelöscht werden", "logoDeletedSuccess": "Logo erfolgreich gelöscht!", "bottleLabelDeletedSuccess": "Flaschenetikett erfolgreich gelöscht!", "editMetadata": "<PERSON><PERSON><PERSON> bearbeiten", "editLogoPerChat": "Logo per Chat bearbeiten", "startMessageForLogo": "Das Logo soll im Stil {{style}} zeigen: {{info}}", "bottleLabel": "Flaschenetikett", "chatWithAi": "Chat mit der KI, um ein Bild zu erstellen", "noBottleLabel": "<PERSON><PERSON> vorhanden", "downloadLogo": "Logo herunterladen", "imageGenerated": "Bild erfolgreich generiert!", "styles": {"comic": "Comic", "anime": "Anime", "manga": "Manga", "abstract": "Abstrakt", "realistic": "Realistisch", "watercolor": "<PERSON><PERSON>", "vintage": "Vintage"}}, "notification": {"push": {"alert": {"title": "Push Notification aktivieren", "body": "Möchten Sie Push Notifications aktivieren?"}, "messages": {"temperatureReached": "Die Temperatur von {{temperature}} °C ist im Gärschrank erreicht."}}, "error": {"general": "<PERSON>s ist ein Fehler aufgetreten.", "saved": "Es ist ein Fehler beim Speichern aufgetreten.", "removed": "<PERSON>s ist ein Fehler beim Entfernen aufgetreten."}, "success": {"saved": "Erfolgreich gespeichert.", "removed": "Erfolgreich entfernt.", "registered": "Registrierung erfolgreich. Sie werden zu Ihrem Account weitergeleitet."}}, "device": {"addDevice": "Ger<PERSON> hinzufügen", "deviceType": "Gerätetyp", "type": {"temperature": "Thermometer", "switch": "<PERSON><PERSON><PERSON>", "ispindel": "iSpindel", "other": "Anderes", "brewtool": "Brewtool"}, "brewing": "<PERSON><PERSON><PERSON>", "fermentation": "Gärung", "mqttClientId": "MQTT Client ID", "mqtt": "MQTT", "createMqtt": "<PERSON><PERSON><PERSON>", "deviceTypeDescription": "<PERSON><PERSON><PERSON><PERSON> Sie aus für was das Geräte genutzt werden soll", "addTuyaDevice": "Tuya Gerät hinzufügen", "tuyaDeviceId": "Tuya Gerät ID", "tuyaDeviceIdNotFound": "Tuya Gerät ID nicht gefunden", "tuyaDeviceIdAlreadyExist": "Ger<PERSON> wurde schon hinzugefügt", "addSmartLifeDevice": "Smart Life hinzufügen", "searchDevices": "<PERSON><PERSON><PERSON>", "searchForDevices": "<PERSON><PERSON> starten", "deviceNotFound": "Gerät nicht gefunden", "password": "Passwort", "ssid": "WLAN Name / SSID", "registerDevice": "Gerät registrieren", "hasInternetConnection": "Internetverbindung vorhanden", "hasNoInternetConnection": "<PERSON>ine Internetverbindung vorhanden", "connectedWithDevice": "Verbunden mit Gerät: {{name}}", "general": "Alle Geräte", "brewingHelpText": "Hier können Sie Geräte fürs Brauen hinzufügen. Dazu gehören zum Beispiel Maischthermostate, Kochgeräte, Hopfenstopfer, etc.", "fermentationHelpText": "Hier können Sie Geräte fürs Gären hinzufügen. Dazu gehören zum Beispiel Gärschränke, Thermometer, etc.", "generalHelpText": "<PERSON>er können Sie alle Geräte verwalten, die Sie fürs Brauen oder Gären verwendet werden.", "name": "G<PERSON><PERSON>", "registerISpindle": "iSpindle registrieren oder konfigurieren", "registerISpindleDescription": "<PERSON><PERSON> Sie die iSpindle regsirieren können, müssen <PERSON> sich mit dem iSpindle W-Lan verbinden."}, "user": {"buttonDeleteAccount": "<PERSON><PERSON>, entferne mein <PERSON>", "deleteAccount": "Konto löschen", "deleteAccountHelp": "Sie möchten unseren Service nicht mehr nutzen? Hier können Sie Ihr Konto löschen. Diese Aktion ist nicht reversibel. Alle mit diesem Konto verbundenen Informationen werden dauerhaft gelöscht.", "fullName": "Name", "email": "E-Mail", "profile": "Profil", "profileHelp": "<PERSON><PERSON> können Sie Ihr Profil bearbeiten", "changePasswordHelp": "<PERSON>er können Sie Ihr Passwort ändern", "changePassword": "Passwort ändern", "confirmPassword": "Passwort wiederholen", "integrationActive": "Integration eingerichtet", "integrationDeactivated": "Integration nicht aktiviert", "account": "Ko<PERSON>", "braureka": {"sectionTitle": "Braureka Integration", "help": "Braureka ist eine Webanwendung, die <PERSON><PERSON> hilft, <PERSON>hre Brauerei zu verwalten. Sie können Rezepte erstellen. Sie können Braureka verwenden, um Rezepte zu erstellen und sie hier automatisch zu importieren."}, "tuya": {"sectionTitle": "Tuya Integration", "help": "<PERSON><PERSON> ist ein Hersteller für smarte Produkte. Mit dieser Integration können Sie Ihre smarten Geräte steuern. Sie können die Temperatur des Gärschranks steuern oder Geräte ein- und ausschalten.", "accessKey": "Access Key", "secretKey": "Secret Key"}, "pushSubscriptions": "<PERSON>ush Benachrichtigungen", "pushSubscriptionsHelp": "<PERSON>er können Sie Push Benachrichtigungenfür verschiedene Geräte verwalten", "browser": "Browser", "os": "System", "product": "Produkt", "createdAt": "Erstellt am", "subscriptionSelf": "<PERSON>ser Browser wurde für Benachrichtigungen registriert", "subscriptionSelfHelp": "Sie können Benachrichtigungen für diesen Browser aktivieren oder deaktivieren", "passkeySectionTitle": "Passkey / Passwortlose Anmeldung", "passkeySectionHelp": "Hier können Sie einen Passkey zu Ihrem Konto hinzufügen oder die Passwort-Anmeldung entfernen.", "removePassword": "Passwort-Anmeldung entfernen", "passkeyAddedSuccess": "Passkey wurde erfolgreich hinzugefügt.", "passkeyAddError": "Fehler beim Hinzufügen des Passkeys.", "passwordRemovedSuccess": "Passwort-Anmeldung wurde entfernt. Sie können sich jetzt nur noch mit Passkey anmelden.", "passwordRemoveError": "Fehler beim Entfernen der Passwort-Anmeldung.", "passkeyOnlyInfo": "Ihr Konto ist jetzt passwortlos. Anmeldung ist nur noch mit Passkey möglich."}, "authentication": {"email": "E-Mail", "password": "Passwort", "login": "Anmelden", "register": "Registrieren", "forgotPassword": "Passwort vergessen?", "rememberMe": "Ang<PERSON><PERSON><PERSON> bleiben", "resetPassword": "Passwort zurücksetzen", "resetPasswordDescription": "Geben Sie Ihre E-Mail-Adresse ein, um Ihr Passwort zurückzusetzen", "resetPasswordButton": "Passwort zurücksetzen", "resetPasswordSuccess": "Eine E-Mail wurde an Ihre E-Mail-Adresse gesendet", "emailOrPasswordWrong": "E-Mail oder Passwort falsch", "signInToYourAccount": "Melden Sie sich bei Ihrem <PERSON> an", "orContinueWith": "oder Anmelden mit", "statusOk": "Alle Systeme arbeiten", "statusProblem": "<PERSON><PERSON> gibt ein Problem mit dem System", "notAMember": "Noch kein Mitglied?", "backToLogin": "<PERSON><PERSON><PERSON> zum Login", "passwordRepeat": "Passwort wiederholen", "registerDontWork": "Registrierung moment nicht möglich", "orUsePasskey": "oder Passkey verwenden", "signInWithPasskey": "<PERSON><PERSON> <PERSON> anmelden", "signUpWithPasskey": "Mit Passkey registrieren", "authenticating": "Authentifizierung...", "webauthnError": "Ein Fehler ist bei der Passkey-Authentifizierung aufgetreten", "passkeyDescription": "<PERSON>erwen<PERSON> Si<PERSON>, Face ID oder andere biometrische Daten", "emailPlaceholder": "<EMAIL>", "passwordsDontMatch": "Passwörter stimmen nicht überein", "passwordAtLeast": "Passwort muss mindestens {{count}} <PERSON><PERSON><PERSON> lang sein", "emailInvalid": "Ungültige E-Mail-Adresse", "repeatPassword": "Passwort wiederholen", "emailAlreadyUsed": "E-Mail wurde schon registriert", "continueWithPassword": "Mit Passwort fortfahren"}, "cold": {"crush": "Cold Crush"}, "success": {"saved": "Gespe<PERSON>rt"}, "error": {"occurred": "<PERSON><PERSON>", "pageNotFoundTitle": "Seite nicht gefunden", "somethingWentWrong": "Etwas ist schiefgelaufen", "pageNotFoundDescription": "<PERSON> Seite, die Si<PERSON> suchen, wurde möglicherweise entfernt, umbenannt oder ist vorübergehend nicht verfügbar.", "helpfulLinks": "Hier sind stattdessen einige hilfreiche Links:", "goHome": "Zur Startsei<PERSON>", "debugInfo": "Debug-Informationen", "contactSupport": "<PERSON><PERSON> dieses Problem weiterhin besteht, wenden Sie sich bitte an den Support."}, "gaerschrank": {"turnedOff": "Gärschrank ausgeschaltet", "turnedOffFailed": "Gärschrank ausschalten fehlgeschlagen"}, "ble": {"connectToBrewtool": "Mit Brewtool verbinden", "unknownBrewtool": "Unbekanntes Brewtool", "unknownError": "Unbekannter Fehler", "selectBrewtool": "Brewtool auswählen", "selectBrewtoolDescription": "Wählen Sie ein Brewtool-Gerät aus der Liste aus", "selectDevice": "Gerät auswählen", "connecting": "Verbin<PERSON>...", "deviceInformation": "Geräteinformationen", "connectionStatus": "Verbindungss<PERSON>us", "internetConnection": "Internetverbindung", "online": "Online", "offline": "Offline", "firmwareVersion": "Firmware-Version", "basicConfiguration": "Grundkonfiguration", "deviceName": "G<PERSON><PERSON><PERSON><PERSON>", "enterDeviceName": "Ger<PERSON><PERSON><PERSON> e<PERSON>ben", "wifiConfiguration": "WLAN-Konfiguration", "enableWifi": "WLAN aktivieren", "wifiNetwork": "WLAN-Netzwerk", "enterWifiName": "WLAN-Name eingeben", "wifiPassword": "WLAN-Passwort", "enterWifiPassword": "WLAN-Passwort eingeben", "scanNetworks": "Netzwerke scannen", "noInternetConnection": "<PERSON>ine <PERSON>bindung", "wifiScanFailed": "WLAN-<PERSON>an fehlgeschlagen", "nameRequired": "Gerätename ist erforderlich", "wifiSSIDRequired": "WLAN-Name ist erford<PERSON>lich", "wifiConfigSaved": "WLAN-Konfiguration gespeichert", "saveError": "Fe<PERSON> beim <PERSON>rn", "connectionError": "Verbindungsfehler", "connectToWifi": "Mit WLAN verbinden", "deviceId": "Device ID", "registerFailed": "Registrierung fehlgeschlagen", "deviceAlreadyRegistered": "Gerät ist bereits von einem anderen Benutzer registriert", "searchDevices": "<PERSON><PERSON><PERSON><PERSON>en", "bluetoothNotSupported": "Bluetooth wird nicht unterstützt", "bluetoothNotSupportedDescription": "Bluetooth wird von Ihrem <PERSON>er nicht unterstützt. Bitte verwenden Si<PERSON> einen anderen Browser oder ein anderes Gerät.", "registerDevice": "Gerät registrieren", "registerDeviceDescription": "Vor der ersten Benutzung muss das Gerät registriert werden."}, "aging": {"addNew": "Neue Reifung hinzufügen", "nameLabel": "Name des Bieres", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON>, IPA, Märzen...", "nameRequired": "Name ist erforderlich", "agingWeeksLabel": "Reifezeit in Wochen", "agingWeeksHelper": "Typische Reifezeiten: Weissbier 2-4 <PERSON><PERSON><PERSON>, <PERSON><PERSON> 4-8 <PERSON><PERSON><PERSON>, Starkbier 8-12 <PERSON><PERSON><PERSON>", "agingWeeksMin": "Mindestens 1 Woche", "fermentationLabel": "Verknüpfte Gärung (optional)", "fermentationNone": "<PERSON>ine Gärung auswählen", "notificationLabel": "Benachrichtigung senden", "notificationHelper": "Erhalte eine Benachrichtigung, wenn die Reifung abgeschlossen ist", "endDateLabel": "Voraussichtliches Ende", "error": "Fehler beim Erstellen der Reifung", "submit": "Reifung hinzufügen", "submitting": "<PERSON><PERSON><PERSON><PERSON>...", "edit": "Reifung bearbeiten", "linkedWith": "Verknüpft mit: {{name}}", "notificationActive": "Benachrichtigung aktiviert", "notificationDeactive": "Benachrichtigung deaktiviert", "ripeningTime": "Reifezeit", "week": "<PERSON><PERSON><PERSON>", "weeks": "<PERSON><PERSON><PERSON>", "startDate": "Startdatum", "endDate": "Enddatum", "completed": "Reifung abgeschlossen", "deleteConfirm": "<PERSON>öchtest du die Reifung \"{{name}}\" wirklich löschen?", "complete": "Abschließen", "completing": "Abschließen...", "delete": "Löschen", "deleting": "Löschen...", "remainingDays_zero": "Heute fertig", "remainingDays_one": "1 Tag verbleibend", "remainingDays_other": "{{count}} Tage verbleibend", "completedDays_one": "1 Tag abgeschlossen", "completedDays_other": "{{count}} <PERSON><PERSON> abgeschlossen", "remainingWeeks_one": "1 Woche verbleibend", "remainingWeeks_other": "{{count}} W<PERSON><PERSON> verbleibend", "completedAging": "Reifung abgeschlossen", "deleted": "Reifung erfolgreich gelöscht"}, "searchBar": {"searchPlaceholder": "Nach Einträgen suchen...", "searchResults": "{{count}} von {{total}} Einträgen gefunden", "clearSearch": "<PERSON><PERSON> löschen", "searchKeyboardHint": "ESC zum Löschen"}}}