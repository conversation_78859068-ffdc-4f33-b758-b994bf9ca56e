import useEditDialog from 'hooks/Dialog/useEditDialog'
import SpindleConfigModal from 'components/Spindle/SpindleConfigModal'
import { SpindleConfig } from 'stores/useSpindleStore'

export default function useSpindleConfigModal() {
  const { openDialog, openEditDialog } = useEditDialog<SpindleConfig, SpindleConfig>(
    'Spindle Configuration',
    SpindleConfigModal,
    data => {
      console.log('Spindle configuration saved:', data)
    }
  )

  return {
    openSpindleConfigModal: openDialog,
    openEditSpindleConfigModal: openEditDialog,
  }
}
