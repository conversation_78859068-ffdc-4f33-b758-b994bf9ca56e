import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useForm, FormProvider } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { CheckCircle, Wifi, WifiOff } from 'lucide-react'

import { SpindleConfig, useSpindleStore } from 'stores/useSpindleStore'
import { createError, createSuccess } from 'hooks/useSimpleNotification'
import { FormComponentProps } from 'hooks/Dialog/useEditDialog'
import { createApiKey, DeviceOperator, DeviceType, registerDevice } from 'services/DeviceService'
import { API_URL } from '../../constants'

import TextInput from 'components/Common/Form/TextInput'
import Checkbox from 'components/Common/Form/Checkbox'
import Button from 'components/Common/Button'

// Schema for form validation
const spindleConfigSchema = z.object({
  s: z.string().min(1, 'WiFi SSID is required'),
  p: z.string().optional(),
  name: z.string().min(1, 'Spindle name is required'),
  sleep: z.coerce.number().min(1).default(900),
  vfact: z.coerce.number().default(1.0),
  tempscale: z.coerce.number().min(0).max(1).default(0),
  selAPI: z.coerce.number().min(0).max(10).default(0),
  warning1: z.string().default(''),
  token: z.string().default(''),
  server: z.string().default(''),
  port: z.coerce.number().min(1).max(65535).default(80),
  channel: z.coerce.number().min(0).default(0),
  uri: z.string().default('/api/ispindel'),
  username: z.string().default(''),
  password: z.string().default(''),
  job: z.string().default(''),
  instance: z.string().default(''),
  POLYN: z.string().default(''),
  registerWithSite: z.boolean().default(false),
})

type SpindleConfigForm = z.infer<typeof spindleConfigSchema>

enum ModalStep {
  FORM = 'form',
  REMOVE_WIFI = 'remove_wifi',
  SUCCESS = 'success',
}

interface SpindleConfigModalProps extends FormComponentProps<SpindleConfig, SpindleConfigForm> {}

export default function SpindleConfigModal({ data, onCancel, onSubmit }: SpindleConfigModalProps) {
  const { t } = useTranslation()
  const [currentStep, setCurrentStep] = useState<ModalStep>(ModalStep.FORM)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [savedFormData, setSavedFormData] = useState<SpindleConfigForm | null>(null)

  const { spindleConfig, mutateSpindleConfig, isConnected } = useSpindleStore()

  console.log(spindleConfig)

  // Initialize form with existing config or defaults
  const methods = useForm<SpindleConfigForm>({
    resolver: zodResolver(spindleConfigSchema),
    defaultValues: spindleConfig ?? {
      s: data?.s || spindleConfig?.s || '',
      p: data?.p || spindleConfig?.p || '',
      name: data?.name || spindleConfig?.name || '',
      sleep: data?.sleep || spindleConfig?.sleep || 900,
      vfact: data?.vfact || spindleConfig?.vfact || 1.0,
      tempscale: data?.tempscale || spindleConfig?.tempscale || 0,
      selAPI: data?.selAPI || spindleConfig?.selAPI || 0,
      warning1: data?.warning1 || spindleConfig?.warning1 || '',
      token: data?.token || spindleConfig?.token || '',
      server: data?.server || spindleConfig?.server || '',
      port: data?.port || spindleConfig?.port || 80,
      channel: data?.channel || spindleConfig?.channel || 0,
      uri: data?.uri || spindleConfig?.uri || '/api/ispindel',
      username: data?.username || spindleConfig?.username || '',
      password: data?.password || spindleConfig?.password || '',
      job: data?.job || spindleConfig?.job || '',
      instance: data?.instance || spindleConfig?.instance || '',
      POLYN: data?.POLYN || spindleConfig?.POLYN || '',
      registerWithSite: false,
    },
  })

  const { watch, handleSubmit } = methods
  const registerWithSite = watch('registerWithSite')

  const onFormSubmit = async (formData: SpindleConfigForm) => {
    setIsSubmitting(true)

    try {
      let configData = { ...formData }

      // If registering with site, set up server configuration and generate API key
      if (registerWithSite) {
        const url = new URL(API_URL)
        configData.server = url.origin
        if (url.port) {
          configData.port = parseInt(url.port)
        } else {
          configData.port = url.protocol === 'https:' ? 443 : 80
        }

        try {
          // Register the spindle device first
          const device = await registerDevice({
            name: formData.name || 'iSpindle',
            operator: DeviceOperator.CUSTOM,
            operatorDeviceId: `ispindle-${Date.now()}`,
            type: DeviceType.ISPINDEL,
          })

          // Create API key for the device
          const apiKey = await createApiKey(device)
          configData.token = apiKey.key
          configData.uri = `/api/device/${device.id}/spindel`

          createSuccess('API key generated successfully')
        } catch (error) {
          console.error('Error generating API key:', error)
          createError('Failed to generate API key')
          setIsSubmitting(false)
          return
        }

        // Save the form data and move to remove WiFi step
        setSavedFormData(configData)
        setCurrentStep(ModalStep.REMOVE_WIFI)
        setIsSubmitting(false)
        return
      }

      // Save configuration using the store's mutate function
      mutateSpindleConfig(configData)

      createSuccess('Configuration saved successfully')
      onSubmit(configData)
    } catch (error) {
      console.error('Error saving spindle configuration:', error)
      createError('Failed to save configuration')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleContinueAfterWifiRemoval = async () => {
    // Now save the configuration after WiFi removal
    if (savedFormData) {
      try {
        mutateSpindleConfig(savedFormData)
        createSuccess('Configuration saved successfully')
        setCurrentStep(ModalStep.SUCCESS)
      } catch (error) {
        console.error('Error saving spindle configuration:', error)
        createError('Failed to save configuration')
      }
    } else {
      setCurrentStep(ModalStep.SUCCESS)
    }
  }

  const handleFinalSuccess = () => {
    createSuccess('Spindle registration complete')
    onSubmit(methods.getValues())
  }

  if (currentStep === ModalStep.REMOVE_WIFI) {
    return (
      <div className="space-y-6 p-4">
        <div className="text-center">
          <div className="mb-4">
            <WifiOff className="mx-auto h-16 w-16 text-orange-500" />
          </div>
          <h3 className="mb-2 text-lg font-medium">{t('spindle.removeWifiTitle')}</h3>
          <p className="mb-4 text-gray-600">{t('spindle.removeWifiDescription')}</p>
        </div>

        <div className="flex justify-center space-x-3">
          <Button
            onClick={handleContinueAfterWifiRemoval}
            className="bg-green-600 hover:bg-green-700"
          >
            {t('common.continue')}
          </Button>
        </div>
      </div>
    )
  }

  if (currentStep === ModalStep.SUCCESS) {
    return (
      <div className="space-y-6 p-4">
        <div className="text-center">
          <div className="mb-4">
            <CheckCircle className="mx-auto h-16 w-16 text-green-500" />
          </div>
          <h3 className="mb-2 text-lg font-medium">{t('spindle.registrationSuccessTitle')}</h3>
          <p className="mb-4 text-gray-600">{t('spindle.registrationSuccessDescription')}</p>
        </div>

        <div className="flex justify-center space-x-3">
          <Button onClick={handleFinalSuccess} className="bg-green-600 hover:bg-green-700">
            {t('spindle.finish')}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
        {/* Connection Status */}
        {isConnected && (
          <div className="rounded-lg bg-green-50 p-4">
            <div className="flex items-center">
              <Wifi className="mr-2 h-5 w-5 text-green-500" />
              <span className="text-sm font-medium text-green-800">
                {t('spindle.connectedToSpindle')}
              </span>
            </div>
          </div>
        )}

        {/* Basic Configuration */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium">{t('spindle.basicConfig')}</h3>

          <TextInput
            name="name"
            label={t('spindle.spindleName')}
            placeholder={t('common.name')}
            aria-autocomplete="none"
          />

          <TextInput
            name="s"
            label={t('spindle.wifiSSID')}
            placeholder={t('spindle.wifiSSID')}
            aria-autocomplete="none"
          />

          <TextInput
            name="p"
            label={t('spindle.wifiPassword')}
            type="password"
            placeholder={t('spindle.wifiPassword')}
          />
        </div>

        {/* Registration Option */}
        <div className="space-y-4">
          <Checkbox name="registerWithSite" label={t('spindle.registerWithSite')} />

          {registerWithSite && (
            <div className="rounded-lg bg-blue-50 p-4">
              <p className="text-sm text-blue-800">{t('spindle.registerInfo')}</p>
            </div>
          )}
        </div>

        {/* Advanced Configuration - only show if not registering with site */}
        {!registerWithSite && (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">{t('spindle.advancedConfig')}</h3>

            <div className="grid grid-cols-2 gap-4">
              <TextInput
                name="server"
                label={t('spindle.serverAddress')}
                placeholder="example.com"
              />

              <TextInput name="port" label={t('spindle.serverPort')} placeholder="80" />
            </div>

            <TextInput name="uri" label={t('spindle.serverPath')} placeholder="/api/ispindel" />

            <TextInput
              name="token"
              label={t('spindle.apiToken')}
              placeholder={t('spindle.apiToken')}
            />

            <div className="grid grid-cols-2 gap-4">
              <TextInput name="sleep" label={t('spindle.sleepInterval')} placeholder="900" />

              <TextInput name="tempscale" label={t('spindle.temperatureScale')} placeholder="0" />
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 border-t pt-4">
          <Button
            type="button"
            onClick={onCancel}
            className="border border-gray-300 bg-white text-gray-700 hover:bg-gray-50"
          >
            {t('common.cancel')}
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
            className="bg-blue-600 text-white hover:bg-blue-700 disabled:opacity-50"
          >
            {isSubmitting ? t('common.saving') : t('common.save')}
          </Button>
        </div>
      </form>
    </FormProvider>
  )
}
