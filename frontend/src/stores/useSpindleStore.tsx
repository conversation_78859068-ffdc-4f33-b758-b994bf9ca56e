import axios from 'axios'
import * as htmlparser2 from 'htmlparser2'
import { getAttributeValue, getElementById } from 'domutils'
import { create } from 'zustand'
import { useEffect } from 'react'
import { API_URL } from 'constants'

export interface SpindleConfig {
  s: string // wifi ssid
  p: string // wifi password
  name: string
  sleep: number
  vfact: number
  tempscale: number
  selAPI: number
  warning1: string
  token: string
  server: string
  port: number
  channel: number
  uri: string
  username: string
  password: string
  job: string
  instance: string
  POLYN: string
}

const FormKeys = [
  's',
  'p',
  'name',
  'sleep',
  'vfact',
  'tempscale',
  'selAPI',
  'warning1',
  'token',
  'server',
  'port',
  'channel',
  'uri',
  'username',
  'password',
  'job',
  'instance',
  'POLYN',
] as (keyof SpindleConfig)[]

const getSpindleWifiConfig = (html: string) => {
  const parser = new DOMParser()
  const dom = parser.parseFromString(html, 'text/html')

  const result = {} as SpindleConfig
  for (const key of FormKeys) {
    const input = dom.getElementById(key)

    if (!input) {
      result[key] = ''
      continue
    }

    console.log(input.value)

    result[key] = (input as unknown as HTMLInputElement).value // dom.('value') ?? ''
  }

  return result
}

type SpindleState = {
  spindleConfig: SpindleConfig | null
  isRegistered: boolean
  isConnected: boolean
  setSpindleConfig: (config: SpindleConfig) => void
  mutateSpindleConfig: (data: unknown) => void
}

export const useSpindleStore = create<SpindleState>()((set, get) => {
  const saveSpindleWifi = async (data: SpindleConfig) => {
    try {
      const searchParams = new URLSearchParams()
      for (const key of FormKeys) {
        searchParams.append(key, String(data[key]))
      }

      const response = await axios.get('http://192.168.4.1/wifisave?' + searchParams.toString())
      console.log(response.data)

      set({ spindleConfig: null, isConnected: false, isRegistered: false })
    } catch (error) {
      console.error('Error fetching WiFi config:', error)
    }
  }

  const mutateSpindleConfig = (data: Partial<SpindleConfig>) => {
    const actualConfig = get().spindleConfig

    const request = { ...actualConfig, ...data } as SpindleConfig

    saveSpindleWifi(request)
  }

  return {
    spindleConfig: null,
    isRegistered: false,
    isConnected: false,
    mutateSpindleConfig,
    setSpindleConfig: config => {
      const url = new URL(API_URL)
      const isRegistered = config.server.startsWith(url.origin)
      set({ spindleConfig: config, isRegistered, isConnected: true })
    },
  }
})

export const useRecognizeSpindle = () => {
  const checkForSpindle = async () => {
    try {
      const res = await axios.get('http://192.168.4.1/wifi')
      console.log(res.data)
      const result = getSpindleWifiConfig(res.data)
      useSpindleStore.getState().setSpindleConfig(result)

      console.log(`Found iSpindle with the name ${result.name}`)
    } catch (error) {
      console.log('Cannot get current SSID!')

      const result = getSpindleWifiConfig(html)
      console.log(result)
      useSpindleStore.getState().setSpindleConfig(result)
    }
  }

  useEffect(() => {
    checkForSpindle()

    const interval = setInterval(checkForSpindle, 15000)

    return () => clearInterval(interval)
  }, [])
}

export const html = `<!doctype html>
<html lang="en">
  <head>
    <meta
      name="viewport"
      charset="utf-8"
      content="width=device-width, initial-scale=1, user-scalable=no"
    />
    <title>Config ESP</title>
    <script>
      var lAPI = [
        {
          name: "Ubidots",
          token: 1,
          server: 0,
          uri: 0,
          port: 0,
          channel: 0,
          username: 0,
          password: 0,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 0,
          usehttps: 0,
        },
        {
          name: "empty",
          token: 1,
          server: 0,
          uri: 0,
          port: 0,
          channel: 0,
          username: 0,
          password: 0,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 0,
          usehttps: 0,
        },
        {
          name: "CraftBeerPi",
          token: 0,
          server: 1,
          uri: 0,
          port: 0,
          channel: 0,
          username: 0,
          password: 0,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 0,
          usehttps: 0,
        },
        {
          name: "HTTP",
          token: 1,
          server: 1,
          uri: 1,
          port: 1,
          channel: 0,
          username: 0,
          password: 0,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 0,
          usehttps: 0,
        },
        {
          name: "TControl",
          token: 0,
          server: 1,
          uri: 0,
          port: 0,
          channel: 0,
          username: 0,
          password: 0,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 0,
          usehttps: 0,
        },
        {
          name: "FHEM",
          token: 0,
          server: 1,
          uri: 0,
          port: 1,
          channel: 0,
          username: 0,
          password: 0,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 0,
          usehttps: 0,
        },
        {
          name: "TCP",
          token: 1,
          server: 1,
          uri: 0,
          port: 1,
          channel: 0,
          username: 0,
          password: 0,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 0,
          usehttps: 0,
        },
        {
          name: "iSpindel.de",
          token: 1,
          server: 0,
          uri: 0,
          port: 0,
          channel: 0,
          username: 0,
          password: 0,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 0,
          usehttps: 0,
        },
        {
          name: "InfluxDB",
          token: 0,
          server: 1,
          uri: 1,
          port: 1,
          channel: 0,
          username: 1,
          password: 1,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 0,
          usehttps: 1,
        },
        {
          name: "Prometheus",
          token: 0,
          server: 1,
          uri: 0,
          port: 1,
          channel: 0,
          username: 0,
          password: 0,
          job: 1,
          instance: 1,
          warning1: 0,
          hassio: 0,
          usehttps: 0,
        },
        {
          name: "MQTT",
          token: 0,
          server: 1,
          uri: 0,
          port: 1,
          channel: 0,
          username: 1,
          password: 1,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 1,
          usehttps: 0,
        },
        {
          name: "ThingSpeak",
          token: 1,
          server: 0,
          uri: 0,
          port: 0,
          channel: 1,
          username: 0,
          password: 0,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 0,
          usehttps: 0,
        },
        {
          name: "Blynk",
          token: 1,
          server: 0,
          uri: 0,
          port: 0,
          channel: 0,
          username: 0,
          password: 0,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 0,
          usehttps: 0,
        },
        {
          name: "Brewblox",
          token: 0,
          server: 1,
          uri: 1,
          port: 1,
          channel: 0,
          username: 1,
          password: 1,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 0,
          usehttps: 0,
        },
        {
          name: "AWSIOTMQTT",
          token: 0,
          server: 1,
          uri: 1,
          port: 1,
          channel: 0,
          username: 0,
          password: 0,
          job: 0,
          instance: 0,
          warning1: 1,
          hassio: 0,
          usehttps: 0,
        },
        {
          name: "HTTPS Post",
          token: 1,
          server: 1,
          uri: 1,
          port: 0,
          channel: 0,
          username: 0,
          password: 0,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 0,
          usehttps: 0,
        },
        {
          name: "BierBot Bricks",
          token: 1,
          server: 0,
          uri: 0,
          port: 0,
          channel: 0,
          username: 0,
          password: 0,
          job: 0,
          instance: 0,
          warning1: 0,
          hassio: 0,
          usehttps: 0,
        },
      ];

      var $ = function (id) {
        return document.getElementById(id);
      };
      var labels = document.getElementsByTagName("LABEL");
      function set(id, show) {
        if ($(id)) {
          $(id).style.display = show ? "block" : "none";
          $(id).label.style.display = show ? "block" : "none";
        }
      }
      function c(l) {
        s.value = l.innerText || l.textContent;
        p.focus();
      }
      function sTS() {
        $("tempscale").value = parseInt(
          $("TS").options[$("TS").selectedIndex].value
        );
      }
      function sAPI(val) {
        $("selAPI").value = val;
        var obj = lAPI[parseInt(val)];
        Object.keys(obj).forEach(function (key) {
          set(key, obj[key]);
        });
      }
      function fillopt() {
        for (el in lAPI) {
          if (lAPI[el].name == "empty") {
            continue;
          }
          var opt = document.createElement("option");
          opt.value = el;
          opt.innerHTML = lAPI[el].name;
          $("API").appendChild(opt);
        }
      }
      window.onload = function (e) {
        for (var i = 0; i < labels.length; i++) {
          if (labels[i].htmlFor != "") {
            var elem = $(labels[i].htmlFor);
            if (elem) elem.label = labels[i];
          }
        }
        value = $("selAPI").value;
        sAPI(value);
        $("TS").value = $("tempscale").value;
        fillopt();
        $("API").querySelector('option[value="' + value + '"]').selected = true;
      };
      window.onchange = function (e) {
        sTS();
      };
    </script>
    <style>
      body,
      textarea,
      input,
      select {
        background: 0;
        border-radius: 0;
        font: 16px sans-serif;
        margin: 0;
      }
      textarea,
      input,
      select {
        outline: 0;
        font-size: 14px;
        border: 1px solid #ccc;
        padding: 8px;
        width: 90%;
      }
      .btn a {
        text-decoration: none;
      }
      .container {
        margin: auto;
        width: 90%;
      }
      @media (min-width: 1200px) {
        .container {
          margin: auto;
          width: 30%;
        }
      }
      @media (min-width: 768px) and (max-width: 1200px) {
        .container {
          margin: auto;
          width: 50%;
        }
      }
      .btn,
      h2 {
        font-size: 2em;
      }
      h1 {
        font-size: 3em;
      }
      .btn {
        background: #0ae;
        border-radius: 4px;
        border: 0;
        color: #fff;
        cursor: pointer;
        display: inline-block;
        margin: 2px 0;
        padding: 10px 14px 11px;
        width: 100%;
      }
      .btn:hover {
        background: #09d;
      }
      .btn:active,
      .btn:focus {
        background: #08b;
      }
      label > * {
        display: inline;
      }
      form > * {
        display: block;
        margin-bottom: 10px;
      }
      textarea:focus,
      input:focus,
      select:focus {
        border-color: #5ab;
      }
      .msg {
        background: #def;
        border-left: 5px solid #59d;
        padding: 1.5em;
      }
      .q {
        float: right;
        width: 64px;
        text-align: right;
      }
      .l {
        background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAALVBMVEX///8EBwfBwsLw8PAzNjaCg4NTVVUjJiZDRUUUFxdiZGSho6OSk5Pg4eFydHTCjaf3AAAAZElEQVQ4je2NSw7AIAhEBamKn97/uMXEGBvozkWb9C2Zx4xzWykBhFAeYp9gkLyZE0zIMno9n4g19hmdY39scwqVkOXaxph0ZCXQcqxSpgQpONa59wkRDOL93eAXvimwlbPbwwVAegLS1HGfZAAAAABJRU5ErkJggg==")
          no-repeat left center;
        background-size: 1em;
      }
      input[type="checkbox"] {
        width: 20px;
      }
      .table td {
        padding: 0.5em;
        text-align: left;
      }
      .table tbody > :nth-child(2n-1) {
        background: #ddd;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h2>Configuration</h2>
      <div>
        <a href="#p" onclick="c(this)">Vodafone Hotspot</a>&nbsp;<span class="q"
          >76%</span
        >
      </div>
      <div>
        <a href="#p" onclick="c(this)">Vodafone-FCC8</a>&nbsp;<span class="q l"
          >76%</span
        >
      </div>
      <div>
        <a href="#p" onclick="c(this)">Vodafone Homespot</a>&nbsp;<span
          class="q"
          >72%</span
        >
      </div>
      <div>
        <a href="#p" onclick="c(this)">Vodafone-B286</a>&nbsp;<span class="q l"
          >32%</span
        >
      </div>
      <div>
        <a href="#p" onclick="c(this)">FRITZ!Box 6660 Cable CB</a>&nbsp;<span
          class="q l"
          >32%</span
        >
      </div>
      <div>
        <a href="#p" onclick="c(this)">Vodafone-3DF6</a>&nbsp;<span class="q l"
          >22%</span
        >
      </div>
      <div>
        <a href="#p" onclick="c(this)">Vodafone-BE13</a>&nbsp;<span class="q l"
          >18%</span
        >
      </div>
      <div>
        <a href="#p" onclick="c(this)">KabelBox-E9E4</a>&nbsp;<span class="q l"
          >16%</span
        >
      </div>
      <div>
        <a href="#p" onclick="c(this)">Hells Kitchen</a>&nbsp;<span class="q l"
          >14%</span
        >
      </div>
      <br />
      <form method="get" action="wifisave">
        <label>SSID</label
        ><input
          id="s"
          name="s"
          length="32"
          placeholder="SSID"
          value="Vodafone&#45;FCC8"
        /><label>Password</label
        ><input
          id="p"
          name="p"
          type="password"
          length="64"
          placeholder="password"
          value="EinLilaPonyReitet123&#33;"
        /><label for="name">iSpindel Name</label
        ><input
          id="name"
          name="name"
          length="4"
          placeholder="iSpindel Name"
          value="Spindel&#95;6"
        /><label for="sleep">Update Interval (s)</label
        ><input
          id="sleep"
          name="sleep"
          length="6"
          placeholder="Update Interval (s)"
          value="900"
          type="number"
          step="any"
        /><label for="vfact">Battery conversion factor</label
        ><input
          id="vfact"
          name="vfact"
          length="7"
          placeholder="Battery conversion factor"
          value="191.80"
          type="number"
          step="any"
        /><label for="TS">Unit of temperature</label>
        <select id="TS" onclick="sTS()">
          <option value="0">Celsius</option>
          <option value="1">Fahrenheit</option>
          <option value="2">Kelvin</option></select
        ><input
          id="tempscale"
          name="tempscale"
          length="5"
          placeholder="tempscale"
          value="0"
          type="hidden"
        />
        <hr />
        <label for="API">Service Type</label>
        <select
          id="API"
          onchange="sAPI(this.options[this.selectedIndex].value);"
        ></select
        ><input
          id="selAPI"
          name="selAPI"
          length="2"
          placeholder="selAPI"
          value="3"
          type="hidden"
        /><label for="warning1"
          >WARNING! Secure MQTT has a big impact on battery usage.<br />&nbsp;<br />For
          AWS:
          <ul>
            <li>Name must be Thingname</li>
            <li>Server must be Endpoint</li>
            <li>Port must be 8883</li>
            <li>Path/URI is Publish Topic</li>
          </ul></label
        ><input
          id="warning1"
          name="warning1"
          length="4"
          placeholder="WARNING! Secure MQTT has a big impact on battery usage.<BR>&nbsp;<BR>For AWS:<UL><LI>Name must be Thingname</LI><LI>Server must be Endpoint</LI><LI>Port must be 8883</LI><LI>Path/URI is Publish Topic</LI></UL>"
          value="<<<<< >>>>>"
        /><label for="token">Token/ API key</label
        ><input
          id="token"
          name="token"
          length="8"
          placeholder="Token/ API key"
          value="YfbcByOk3yzxgXVyeQZrhhbbbgc"
        /><label for="server">Server Address</label
        ><input
          id="server"
          name="server"
          length="2"
          placeholder="Server Address"
          value="http://lucky-cheetah-41.telebit.io"
        /><label for="port">Server Port</label
        ><input
          id="port"
          name="port"
          length="4"
          placeholder="Server Port"
          value="80"
          type="number"
          step="any"
        /><label for="channel">Channelnumber</label
        ><input
          id="channel"
          name="channel"
          length="4"
          placeholder="Channelnumber"
          value="0"
          type="number"
          step="any"
        /><label for="uri">Path / URI</label
        ><input
          id="uri"
          name="uri"
          length="2"
          placeholder="Path / URI"
          value=""
        /><label for="username">Username</label
        ><input
          id="username"
          name="username"
          length="4"
          placeholder="Username"
          value="Test"
        /><label for="password">Password</label
        ><input
          id="password"
          name="password"
          length="2"
          placeholder="Password"
          value="test"
        /><label for="job">Prometheus job</label
        ><input
          id="job"
          name="job"
          length="4"
          placeholder="Prometheus job"
          value="ispindel"
        /><label for="instance">Prometheus instance</label
        ><input
          id="instance"
          name="instance"
          length="4"
          placeholder="Prometheus instance"
          value="000"
        /><label for="hassio">Home Assistant integration via MQTT</label
        ><input
          id="hassio"
          name="hassio"
          length="4"
          placeholder="Home Assistant integration via MQTT"
          value="checked"
          type="checkbox"
        /><label for="usehttps">Connect to server via HTTPS</label
        ><input
          id="usehttps"
          name="usehttps"
          length="4"
          placeholder="Connect to server via HTTPS"
          value="checked"
          type="checkbox"
        />
        <hr />
        <label for="POLYN"
          >Gravity conversion<br />ex.
          "-0.00031*tilt^2+0.557*tilt-14.054"</label
        ><label for="POLYN">Polynominal</label
        ><input
          id="POLYN"
          name="POLYN"
          length="2"
          placeholder="Polynominal"
          value="&#45;0&#46;00031&#42;tilt&#94;2&#43;0&#46;557&#42;tilt&#45;14&#46;054"
        /><br /><button class="btn" type="submit">save</button>
      </form>
    </div>
  </body>
</html>
`
