import { PlusIcon, TrashIcon } from '@heroicons/react/24/outline'
import type { Device } from '@prisma/client'
import { iconUrl } from 'constants'
import { ReactElement } from 'react'
import { useTranslation } from 'react-i18next'
import useIntegrationStore from 'stores/IntegrationStore'
import useSpindleConfigModal from 'hooks/useSpindleConfigModal'

import tuyaIcon from 'assets/tuya.png'

import { del, post, put, useGet, useHttpNotifications } from 'helpers/http'
import useDeleteDialog from 'hooks/Dialog/useDeleteDialog'
import useEditDialog from 'hooks/Dialog/useEditDialog'

import Button, { ButtonWithIcon } from 'components/Common/Button'
import { EmptyListBox } from 'components/Common/EmptyListBox'
import SkeletonLoading from 'components/Common/SkeletonLoading'
import EditForm from 'components/Device/EditForm'
import SelectDeviceForm from 'components/Device/SelectDeviceForm'
import SmartLifeForm from 'components/Device/SmartLifeForm'
import TuyaForm from 'components/Device/TuyaForm'
import DeviceCard from 'components/Settings/DeviceCard'

export default function DeviceSettingsPage(): ReactElement {
  const { t } = useTranslation()
  const resolveResponse = useHttpNotifications()
  const integration = useIntegrationStore.getState()
  const { openSpindleConfigModal } = useSpindleConfigModal()

  const reqFermentationDevices = useGet<Device[]>('fermentation/device')

  const reqBrewDevices = useGet<Device[]>('brewing/device')

  const { openDialog } = useEditDialog<Device, unknown>(t('device.name'), EditForm, async data => {
    await resolveResponse(() => post('device', { ...data, operator: 'CUSTOM' }))
    deviceRequest.refetch()
  })

  const { openDialog: openTuyaDialog } = useEditDialog<Device>(
    t('device.addTuyaDevice'),
    TuyaForm,
    async (data, initData) => {
      if (initData) {
        await resolveResponse(() => put(`device/${initData.id}`, data))
      } else {
        await resolveResponse(() => post('device', data))
      }
      deviceRequest.refetch()
    }
  )

  const { openDialog: openSmartLifeDialog } = useEditDialog<Device>(
    t('device.addSmartLifeDevice'),
    SmartLifeForm,
    async (data, initData) => {
      deviceRequest.refetch()
    }
  )

  const openDeleteDialogForBrewing = useDeleteDialog(async data => {
    await resolveResponse(() => del(`brewing/device/${data.id}`))
    reqBrewDevices.refetch()
  })

  const deviceRequest = useGet<Device[]>('device')

  const { openDialog: openBrewDialog } = useEditDialog(
    t('device.name'),
    SelectDeviceForm,
    async data => {
      await resolveResponse(() =>
        post('brewing/device', {
          deviceId: data.id,
          type: data.type,
        })
      )
      reqBrewDevices.refetch()
    }
  )

  return (
    <div className="flex flex-col gap-3 space-y-6">
      <section>
        <div className="mb-3 flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold">{t('device.fermentation')}</h2>
            <p className="mb-2 text-sm text-gray-500">{t('device.fermentationHelpText')}</p>
          </div>
          <div className="flex gap-2">
            <ButtonWithIcon onClick={openSpindleConfigModal} style="primary" icon={false}>
              Configure iSpindle
            </ButtonWithIcon>
            <ButtonWithIcon onClick={openBrewDialog} style="secondary" icon={true}>
              <PlusIcon className="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
              {t('device.addDevice')}
            </ButtonWithIcon>
          </div>
        </div>
        {reqFermentationDevices.isLoading && <SkeletonLoading />}
        {!reqFermentationDevices.isLoading && (
          <EmptyListBox data={reqFermentationDevices.data}></EmptyListBox>
        )}
        {reqFermentationDevices.data && (
          <ul role="list" className="divide-y divide-gray-100">
            {reqFermentationDevices.data?.map(device => (
              <li key={device.id} className="flex justify-between gap-x-6 py-5">
                <div className="flex min-w-0 gap-x-4">
                  <img
                    className="h-12 w-12 flex-none rounded-full bg-gray-50"
                    src={device.tuyaDeviceId !== null ? tuyaIcon : iconUrl}
                    alt=""
                  />
                  <div className="min-w-0 flex-auto">
                    <p className="text-sm font-semibold leading-6 text-gray-900">{device.name}</p>
                    <p className="mt-1 truncate text-xs leading-5 text-gray-500">{device.type}</p>
                  </div>
                </div>
                <div className="flex shrink-0 items-center">
                  <Button
                    style="secondary"
                    size="sm"
                    icon={true}
                    onClick={e => openDeleteDialogForBrewing(device)}
                  >
                    <TrashIcon className="h-5 w-5" aria-hidden="true" />
                  </Button>
                </div>
              </li>
            ))}
          </ul>
        )}
      </section>

      <hr className="border-gray-300" />

      <section>
        <div className="mb-3 flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold">{t('device.brewing')}</h2>
            <p className="mb-2 text-sm text-gray-500">{t('device.brewingHelpText')}</p>
          </div>
          <div>
            <ButtonWithIcon onClick={openBrewDialog} style="secondary" icon={true}>
              <PlusIcon className="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
              {t('device.addDevice')}
            </ButtonWithIcon>
          </div>
        </div>
        {reqBrewDevices.isLoading && <SkeletonLoading />}
        {!reqBrewDevices.isLoading && <EmptyListBox data={reqBrewDevices.data}></EmptyListBox>}
        {reqBrewDevices.data && (
          <ul role="list" className="divide-y divide-gray-100">
            {reqBrewDevices.data?.map(device => (
              <li key={device.id} className="flex justify-between gap-x-6 py-5">
                <div className="flex min-w-0 gap-x-4">
                  <img
                    className="h-12 w-12 flex-none rounded-full bg-gray-50"
                    src={device.tuyaDeviceId !== null ? tuyaIcon : iconUrl}
                    alt=""
                  />
                  <div className="min-w-0 flex-auto">
                    <p className="text-sm font-semibold leading-6 text-gray-900">{device.name}</p>
                    <p className="mt-1 truncate text-xs leading-5 text-gray-500">{device.type}</p>
                  </div>
                </div>
                <div className="flex shrink-0 items-center">
                  <Button
                    style="secondary"
                    size="sm"
                    icon={true}
                    onClick={e => openDeleteDialogForBrewing(device)}
                  >
                    <TrashIcon className="h-5 w-5" aria-hidden="true" />
                  </Button>
                </div>
              </li>
            ))}
          </ul>
        )}
      </section>

      <hr className="border-gray-300" />

      <section>
        <div className="mb-3 flex items-center justify-between">
          <div>
            <h2 className="text-lg font-semibold">{t('device.general')}</h2>
            <p className="mb-2 text-sm text-gray-500">{t('device.generalHelpText')}</p>
          </div>
          <div className="flex items-center space-x-2">
            <Button onClick={openTuyaDialog} disabled={!integration.tuya} style="secondary">
              {t('device.addTuyaDevice')}
            </Button>

            <ButtonWithIcon
              onClick={openSmartLifeDialog}
              disabled={!integration.smartlife}
              style="secondary"
              icon={true}
            >
              <PlusIcon className="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
              {t('device.addSmartLifeDevice')}
            </ButtonWithIcon>

            <ButtonWithIcon onClick={openDialog} style="primary" icon={true}>
              <PlusIcon className="-ml-0.5 mr-1.5 h-5 w-5" aria-hidden="true" />
              {t('device.addDevice')}
            </ButtonWithIcon>
          </div>
        </div>

        <div className="mt-5">
          <EmptyListBox data={deviceRequest.data} onAdd={openDialog} />
        </div>

        {deviceRequest.isLoading && <SkeletonLoading />}

        <ul role="list" className="grid grid-cols-1 gap-x-6 gap-y-8 lg:grid-cols-3 xl:gap-x-8">
          {deviceRequest.data?.map(device => (
            <DeviceCard key={device.id} device={device} onRefresh={deviceRequest.refetch} />
          ))}
        </ul>
      </section>
    </div>
  )
}
