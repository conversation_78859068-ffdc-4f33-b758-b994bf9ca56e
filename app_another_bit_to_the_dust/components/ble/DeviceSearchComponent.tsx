import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
  Switch,
} from "react-native";
import { useTranslation } from "react-i18next";
import BLEService, { DiscoveredDevice } from "../../../app/services/BLEService";
import BrewtoolConnectionModal from "./BrewtoolConnectionModal";

interface DeviceSearchComponentProps {
  onDeviceFound?: (devices: DiscoveredDevice[]) => void;
  onDeviceSelect?: (device: DiscoveredDevice) => void;
  autoStart?: boolean;
  brewToolsOnly?: boolean;
}

export default function DeviceSearchComponent({
  onDeviceFound,
  onDeviceSelect,
  autoStart = false,
  brewToolsOnly = true,
}: DeviceSearchComponentProps) {
  const { t } = useTranslation();
  const [isScanning, setIsScanning] = useState(false);
  const [discoveredDevices, setDiscoveredDevices] = useState<
    DiscoveredDevice[]
  >([]);
  const [brewToolsOnlyFilter, setBrewToolsOnlyFilter] = useState(brewToolsOnly);
  const [bluetoothEnabled, setBluetoothEnabled] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState<DiscoveredDevice | null>(
    null
  );
  const [showConnectionModal, setShowConnectionModal] = useState(false);

  // Check Bluetooth status
  const checkBluetoothStatus = useCallback(async () => {
    try {
      const enabled = await BLEService.isBluetoothEnabled();
      setBluetoothEnabled(enabled);
      return enabled;
    } catch (error) {
      console.error("Error checking Bluetooth status:", error);
      return false;
    }
  }, []);

  // Handle scan results
  const handleScanResults = useCallback(
    (devices: DiscoveredDevice[]) => {
      const filteredDevices = brewToolsOnlyFilter
        ? devices.filter((device) => device.isBrewtool)
        : devices;

      setDiscoveredDevices(filteredDevices);
      onDeviceFound?.(filteredDevices);
    },
    [brewToolsOnlyFilter, onDeviceFound]
  );

  // Start scanning
  const startScan = useCallback(async () => {
    try {
      const enabled = await checkBluetoothStatus();
      if (!enabled) {
        Alert.alert(
          t("ble.bluetoothDisabled"),
          t("ble.enableBluetoothMessage"),
          [{ text: t("common.ok") }]
        );
        return;
      }

      setIsScanning(true);
      setDiscoveredDevices([]);

      await BLEService.startScan(brewToolsOnlyFilter);

      // Auto-stop after 30 seconds
      setTimeout(() => {
        stopScan();
      }, 30000);
    } catch (error) {
      console.error("Scan start failed:", error);
      setIsScanning(false);

      Alert.alert(
        t("ble.scanError"),
        error instanceof Error ? error.message : t("ble.unknownError"),
        [{ text: t("common.ok") }]
      );
    }
  }, [brewToolsOnlyFilter, checkBluetoothStatus, t]);

  // Stop scanning
  const stopScan = useCallback(() => {
    BLEService.stopScan();
    setIsScanning(false);
  }, []);

  // Handle device selection
  const handleDeviceSelect = useCallback(
    (device: DiscoveredDevice) => {
      // For Brewtool devices, show the connection modal
      if (device.isBrewtool) {
        setSelectedDevice(device);
        setShowConnectionModal(true);
        return;
      }

      // For other devices, use the callback directly
      onDeviceSelect?.(device);
    },
    [onDeviceSelect]
  );

  // Handle modal connect
  const handleModalConnect = useCallback(
    async (device: DiscoveredDevice, config?: any) => {
      try {
        onDeviceSelect?.(device);
        setShowConnectionModal(false);
      } catch (error) {
        // Error handling is done in the modal
        throw error;
      }
    },
    [onDeviceSelect]
  );

  // Handle modal save
  const handleModalSave = useCallback(
    (device: DiscoveredDevice, config: any) => {
      // Save configuration logic here
      console.log("Saving Brewtool configuration:", {
        device: device.id,
        config,
      });
      setShowConnectionModal(false);
    },
    []
  );

  // Setup listeners
  useEffect(() => {
    BLEService.addScanListener(handleScanResults);

    return () => {
      BLEService.removeScanListener(handleScanResults);
    };
  }, [handleScanResults]);

  // Auto-start scan if requested
  useEffect(() => {
    if (autoStart) {
      checkBluetoothStatus().then((enabled) => {
        if (enabled) {
          startScan();
        }
      });
    }
  }, [autoStart, startScan, checkBluetoothStatus]);

  // Check Bluetooth status on mount
  useEffect(() => {
    checkBluetoothStatus();
  }, [checkBluetoothStatus]);

  const getDeviceIcon = (device: DiscoveredDevice) => {
    if (device.isBrewtool) {
      return "🍺";
    }
    return "📱";
  };

  const getSignalStrength = (rssi: number) => {
    if (rssi > -50) return "📶";
    if (rssi > -70) return "📶";
    if (rssi > -80) return "📶";
    return "📶";
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>{t("ble.deviceSearch")}</Text>

        {/* Bluetooth Status */}
        <View style={styles.statusContainer}>
          <View
            style={[
              styles.statusIndicator,
              { backgroundColor: bluetoothEnabled ? "#4CAF50" : "#F44336" },
            ]}
          />
          <Text style={styles.statusText}>
            {bluetoothEnabled ? t("ble.bluetoothOn") : t("ble.bluetoothOff")}
          </Text>
        </View>
      </View>

      {/* Controls */}
      <View style={styles.controls}>
        {/* Filter Toggle */}
        <View style={styles.filterContainer}>
          <Text style={styles.filterLabel}>{t("ble.brewToolsOnly")}</Text>
          <Switch
            value={brewToolsOnlyFilter}
            onValueChange={setBrewToolsOnlyFilter}
            disabled={isScanning}
          />
        </View>

        {/* Scan Button */}
        <TouchableOpacity
          style={[
            styles.scanButton,
            isScanning && styles.scanButtonActive,
            !bluetoothEnabled && styles.scanButtonDisabled,
          ]}
          onPress={isScanning ? stopScan : startScan}
          disabled={!bluetoothEnabled}
        >
          {isScanning && (
            <ActivityIndicator
              size="small"
              color="white"
              style={styles.spinner}
            />
          )}
          <Text style={styles.scanButtonText}>
            {isScanning ? t("ble.stopScan") : t("ble.startScan")}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Device Count */}
      <View style={styles.deviceCount}>
        <Text style={styles.deviceCountText}>
          {t("ble.devicesFound", { count: discoveredDevices.length })}
        </Text>
        {brewToolsOnlyFilter && (
          <Text style={styles.brewToolCount}>
            ({discoveredDevices.filter((d) => d.isBrewtool).length}{" "}
            {t("ble.brewTools")})
          </Text>
        )}
      </View>

      {/* Device List */}
      <View style={styles.deviceList}>
        {discoveredDevices.length === 0 && !isScanning && (
          <View style={styles.emptyState}>
            <Text style={styles.emptyStateText}>{t("ble.noDevicesFound")}</Text>
            <Text style={styles.emptyStateSubtext}>
              {t("ble.startScanToFind")}
            </Text>
          </View>
        )}

        {discoveredDevices.map((device) => (
          <TouchableOpacity
            key={device.id}
            style={[
              styles.deviceItem,
              device.isBrewtool && styles.brewToolDevice,
            ]}
            onPress={() => handleDeviceSelect(device)}
          >
            <View style={styles.deviceInfo}>
              <View style={styles.deviceHeader}>
                <Text style={styles.deviceIcon}>{getDeviceIcon(device)}</Text>
                <Text style={styles.deviceName}>
                  {device.name || device.localName || t("ble.unknownDevice")}
                </Text>
                {device.isBrewtool && (
                  <View style={styles.brewToolBadge}>
                    <Text style={styles.brewToolBadgeText}>
                      {t("ble.brewTool")}
                    </Text>
                  </View>
                )}
              </View>

              <View style={styles.deviceDetails}>
                <Text style={styles.deviceId}>{device.id}</Text>
                <View style={styles.deviceMeta}>
                  <Text style={styles.rssi}>
                    {getSignalStrength(device.rssi)} {device.rssi} dBm
                  </Text>
                  {device.isConnectable && (
                    <Text style={styles.connectable}>
                      {t("ble.connectable")}
                    </Text>
                  )}
                </View>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {/* Scanning Indicator */}
      {isScanning && (
        <View style={styles.scanningIndicator}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.scanningText}>{t("ble.scanning")}</Text>
        </View>
      )}

      {/* Connection Modal */}
      <BrewtoolConnectionModal
        visible={showConnectionModal}
        device={selectedDevice}
        onClose={() => setShowConnectionModal(false)}
        onConnect={handleModalConnect}
        onSave={handleModalSave}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    color: "#666",
  },
  controls: {
    marginBottom: 16,
  },
  filterContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
    paddingVertical: 8,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: "500",
  },
  scanButton: {
    backgroundColor: "#007AFF",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  scanButtonActive: {
    backgroundColor: "#FF3B30",
  },
  scanButtonDisabled: {
    backgroundColor: "#ccc",
  },
  scanButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  spinner: {
    marginRight: 8,
  },
  deviceCount: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  deviceCountText: {
    fontSize: 14,
    fontWeight: "500",
    marginRight: 8,
  },
  brewToolCount: {
    fontSize: 12,
    color: "#666",
  },
  deviceList: {
    flex: 1,
  },
  emptyState: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
    textAlign: "center",
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: "#666",
    textAlign: "center",
  },
  deviceItem: {
    backgroundColor: "white",
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  brewToolDevice: {
    borderLeftWidth: 4,
    borderLeftColor: "#4CAF50",
  },
  deviceInfo: {
    flex: 1,
  },
  deviceHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  deviceIcon: {
    fontSize: 20,
    marginRight: 8,
  },
  deviceName: {
    fontSize: 16,
    fontWeight: "600",
    flex: 1,
  },
  brewToolBadge: {
    backgroundColor: "#4CAF50",
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  brewToolBadgeText: {
    color: "white",
    fontSize: 10,
    fontWeight: "600",
  },
  deviceDetails: {
    marginTop: 4,
  },
  deviceId: {
    fontSize: 12,
    color: "#666",
    fontFamily: "monospace",
    marginBottom: 2,
  },
  deviceMeta: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  rssi: {
    fontSize: 12,
    color: "#666",
  },
  connectable: {
    fontSize: 12,
    color: "#4CAF50",
    fontWeight: "500",
  },
  scanningIndicator: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(255, 255, 255, 0.9)",
    justifyContent: "center",
    alignItems: "center",
  },
  scanningText: {
    marginTop: 8,
    fontSize: 16,
    color: "#007AFF",
  },
});
