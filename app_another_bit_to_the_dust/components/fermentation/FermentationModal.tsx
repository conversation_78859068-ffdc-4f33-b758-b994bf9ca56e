import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  ModalFooter,
} from "@/trash/ui/modal";
import { Button, ButtonText } from "@/trash/ui/button";
import { Input, InputField } from "@/trash/ui/input";
import { VStack } from "@/trash/ui/vstack";
import { HStack } from "@/trash/ui/hstack";
import { Heading } from "@/trash/ui/heading";
import { Text } from "@/trash/ui/text";
import { XMarkIcon } from "react-native-heroicons/solid";

type FermentationModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: { title: string; targetTemp: string }) => void;
  initialTitle?: string;
};

const FermentationModal = ({
  isOpen,
  onClose,
  onSubmit,
  initialTitle = "",
}: FermentationModalProps) => {
  const { t } = useTranslation();
  const [title, setTitle] = useState(initialTitle);
  const [targetTemp, setTargetTemp] = useState("19.0");

  const handleSubmit = () => {
    onSubmit({
      title,
      targetTemp,
    });
    onClose();
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <ModalBackdrop />
      <ModalContent>
        <ModalHeader>
          <Heading size="lg">{t("fermentation.startFermentation")}</Heading>
          <ModalCloseButton>
            <XMarkIcon size={20} color="#6b7280" />
          </ModalCloseButton>
        </ModalHeader>

        <ModalBody>
          <VStack space="lg">
            <VStack space="sm">
              <Text className="font-medium text-gray-700">
                {t("fermentation.recipeName")}
              </Text>
              <Input>
                <InputField
                  placeholder={t("fermentation.recipeNamePlaceholder")}
                  value={title}
                  onChangeText={setTitle}
                />
              </Input>
            </VStack>

            <VStack space="sm">
              <Text className="font-medium text-gray-700">
                {t("fermentation.targetTemperature")} (°C)
              </Text>
              <Input>
                <InputField
                  placeholder="19.0"
                  value={targetTemp}
                  onChangeText={setTargetTemp}
                  keyboardType="numeric"
                />
              </Input>
            </VStack>
          </VStack>
        </ModalBody>

        <ModalFooter>
          <HStack space="md" className="w-full">
            <Button variant="outline" className="flex-1" onPress={onClose}>
              <ButtonText>{t("common.cancel")}</ButtonText>
            </Button>
            <Button className="flex-1 bg-green-600" onPress={handleSubmit}>
              <ButtonText>{t("fermentation.start")}</ButtonText>
            </Button>
          </HStack>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

export default FermentationModal;
