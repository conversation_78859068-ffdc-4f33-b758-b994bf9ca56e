import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import { Link } from 'expo-router';
import AuthGuard from '@/components/auth/AuthGuard';
import { useWidgetUpdate } from '@/hooks/useWidgetUpdate';

export default function HomeScreen() {
  const { t } = useTranslation();
  const { widgetData, refreshWidget } = useWidgetUpdate();

  return (
    <AuthGuard>
      <View style={styles.container}>
        <Text style={styles.title}>{t('app.welcome')}</Text>
        
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('fermentation.title')}</Text>
          <Link href="/fermentation" style={styles.link}>
            <Text style={styles.linkText}>{t('fermentation.viewAll')}</Text>
          </Link>
        </View>

        {widgetData && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>{t('widget.currentFermentation')}</Text>
            <View style={styles.widgetPreview}>
              <Text style={styles.fermentationName}>{widgetData.fermentationName}</Text>
              {widgetData.currentTemperature && (
                <Text style={styles.temperature}>
                  {widgetData.currentTemperature.toFixed(1)}°C
                </Text>
              )}
              {widgetData.gravity && (
                <Text style={styles.gravity}>
                  {widgetData.gravity.toFixed(1)}°P
                </Text>
              )}
              <Text style={styles.status}>{t(`widget.${widgetData.status}`)}</Text>
            </View>
          </View>
        )}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>{t('device.bluetooth')}</Text>
          <Link href="/device" style={styles.link}>
            <Text style={styles.linkText}>{t('device.manage')}</Text>
          </Link>
        </View>
      </View>
    </AuthGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    textAlign: 'center',
  },
  section: {
    marginBottom: 25,
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
  },
  link: {
    padding: 10,
    backgroundColor: '#007AFF',
    borderRadius: 5,
    alignItems: 'center',
  },
  linkText: {
    color: 'white',
    fontWeight: '500',
  },
  widgetPreview: {
    padding: 10,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
  },
  fermentationName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
  },
  temperature: {
    fontSize: 14,
    color: '#FF6B35',
    marginBottom: 3,
  },
  gravity: {
    fontSize: 14,
    color: '#007AFF',
    marginBottom: 3,
  },
  status: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
});
