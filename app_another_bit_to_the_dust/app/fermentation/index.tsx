import React from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { Link } from 'expo-router';
import { useQuery } from '@tanstack/react-query';
import AuthGuard from '@/components/auth/AuthGuard';
import { useWidgetDataSync } from '@/hooks/useWidgetUpdate';
import { get } from '@/helpers/http';
import { Brewtool } from '@/services/BrewtoolService';

export default function FermentationScreen() {
  const { t } = useTranslation();

  const {
    data: brewtools,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ['brewtools'],
    queryFn: async () => {
      const response = await get('brewtool');
      return response.json() as Promise<Brewtool[]>;
    },
  });

  // Sync widget data when brewtools change
  useWidgetDataSync([brewtools]);

  const renderBrewtool = ({ item }: { item: Brewtool }) => (
    <Link href={`/fermentation/device/${item.id}`} asChild>
      <TouchableOpacity style={styles.card}>
        <View style={styles.cardHeader}>
          <Text style={styles.cardTitle}>
            {item.config?.recipeName || item.name || `Device ${item.id}`}
          </Text>
          <View style={[styles.statusBadge, getStatusStyle(item)]}>
            <Text style={styles.statusText}>{getStatusText(item, t)}</Text>
          </View>
        </View>

        <View style={styles.cardContent}>
          {item.statistic?.temperature && (
            <View style={styles.dataRow}>
              <Text style={styles.dataLabel}>{t('fermentation.temperature')}:</Text>
              <Text style={styles.dataValue}>
                {item.statistic.temperature.toFixed(1)}°C
              </Text>
            </View>
          )}

          {item.config?.temperature && (
            <View style={styles.dataRow}>
              <Text style={styles.dataLabel}>{t('fermentation.target')}:</Text>
              <Text style={styles.dataValue}>
                {item.config.temperature.toFixed(1)}°C
              </Text>
            </View>
          )}

          {item.plato && (
            <View style={styles.dataRow}>
              <Text style={styles.dataLabel}>{t('fermentation.gravity')}:</Text>
              <Text style={styles.dataValue}>{item.plato.toFixed(1)}°P</Text>
            </View>
          )}

          {item.statistic?.updatedAt && (
            <View style={styles.dataRow}>
              <Text style={styles.dataLabel}>{t('common.lastUpdate')}:</Text>
              <Text style={styles.dataValue}>
                {new Date(item.statistic.updatedAt).toLocaleString()}
              </Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    </Link>
  );

  const getStatusStyle = (item: Brewtool) => {
    if (!item.statistic?.updatedAt) return styles.statusOffline;
    
    const lastUpdate = new Date(item.statistic.updatedAt);
    const now = new Date();
    const diffMinutes = (now.getTime() - lastUpdate.getTime()) / (1000 * 60);
    
    if (diffMinutes > 10) return styles.statusOffline;
    if (!item.config?.temperature) return styles.statusStandby;
    if (item.config.temperature === 4) return styles.statusColdCrash;
    return styles.statusActive;
  };

  const getStatusText = (item: Brewtool, t: any) => {
    if (!item.statistic?.updatedAt) return t('widget.offline');
    
    const lastUpdate = new Date(item.statistic.updatedAt);
    const now = new Date();
    const diffMinutes = (now.getTime() - lastUpdate.getTime()) / (1000 * 60);
    
    if (diffMinutes > 10) return t('widget.offline');
    if (!item.config?.temperature) return t('widget.standby');
    if (item.config.temperature === 4) return t('widget.coldCrash');
    return t('widget.fermentationActive');
  };

  return (
    <AuthGuard>
      <View style={styles.container}>
        {isLoading ? (
          <View style={styles.centered}>
            <Text>{t('common.loading')}</Text>
          </View>
        ) : !brewtools || brewtools.length === 0 ? (
          <View style={styles.centered}>
            <Text style={styles.emptyText}>{t('fermentation.noDevices')}</Text>
            <Text style={styles.emptySubtext}>{t('fermentation.addDevice')}</Text>
          </View>
        ) : (
          <FlatList
            data={brewtools}
            renderItem={renderBrewtool}
            keyExtractor={(item) => item.id.toString()}
            refreshControl={
              <RefreshControl refreshing={isLoading} onRefresh={refetch} />
            }
            contentContainerStyle={styles.listContainer}
          />
        )}
      </View>
    </AuthGuard>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  listContainer: {
    padding: 15,
  },
  centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  card: {
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: 'white',
  },
  statusActive: {
    backgroundColor: '#4CAF50',
  },
  statusColdCrash: {
    backgroundColor: '#2196F3',
  },
  statusStandby: {
    backgroundColor: '#9E9E9E',
  },
  statusOffline: {
    backgroundColor: '#F44336',
  },
  cardContent: {
    gap: 5,
  },
  dataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dataLabel: {
    fontSize: 14,
    color: '#666',
  },
  dataValue: {
    fontSize: 14,
    fontWeight: '500',
  },
});
