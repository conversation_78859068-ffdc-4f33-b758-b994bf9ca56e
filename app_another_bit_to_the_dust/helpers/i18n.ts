import i18n from "i18next";
import { initReactI18next } from "react-i18next";
import { getLocales } from "expo-localization";
import de from "@/locales/de.json";
import frontendDe from "@/locales/de_frontend.json";

const resources = {
  "de-DE": { translation: { ...frontendDe.translation, ...de.translation } },
};

// const resources = {
//   "de-DE": de,
// };

const initI18n = async () => {
  const locale = getLocales()[0];

  i18n.use(initReactI18next).init({
    compatibilityJSON: "v3",
    resources,
    lng: locale.languageTag,
    fallbackLng: "de-DE",
    interpolation: {
      escapeValue: false,
    },
  });
};

initI18n();

export default i18n;
